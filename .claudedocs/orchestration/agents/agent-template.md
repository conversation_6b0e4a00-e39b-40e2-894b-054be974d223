# Agent XX: [Name] Agent Tracker

## Mission
[Clear mission statement - what this agent accomplishes]

## Priority: [🔴 CRITICAL | 🟡 HIGH | 🟢 MEDIUM | 🔵 LOW]
[Any blocking dependencies or parallel execution notes]

## Research Documents
- [ ] Document 1: [path/to/research.md]
- [ ] Document 2: [path/to/research.md]
- [ ] [Additional research as needed]

## Tasks
- [ ] Task 1: [Specific, measurable task]
- [ ] Task 2: [Specific, measurable task]
- [ ] Task 3: [Specific, measurable task]
- [ ] [Additional tasks as needed]

## Files to Modify
| File | Changes | Status |
|------|---------|--------|
| `path/to/file` | Description of changes | Pending |
| `path/to/file` | Description of changes | Pending |

## Evidence to Collect
- `evidence/agent-XX/[name].txt` - Description
- `evidence/agent-XX/[name].md` - Description
- `evidence/agent-XX/[name].patch` - Git diff of changes
- `evidence/agent-XX/validation.txt` - Validation results

## Findings
[Key findings discovered during agent execution]

## Dependencies
- Agent XX: [Dependency description]
- [Additional dependencies]

## Handoff Notes
[Information that dependent agents need to know]
[Key decisions made]
[Remaining work or considerations]

## Recovery Instructions
```bash
# To recover this agent's context:
/agent-status agent-XX-[name]

# Key commands or files to review:
[Specific recovery commands]

# Continue from: [Checkpoint description]
```

## Status: [NOT STARTED | BLOCKED | IN PROGRESS | COMPLETED]
Progress: X%
Last Updated: YYYY-MM-DD