# PRP Execution Guide

## Overview
This guide documents how to execute a generated PRP (Product Requirements Prompt) using the proper slash command syntax.

## Execution Command

### Basic Syntax
```
/execute-prp --persona-[type] --seq @PRPs/active/[prp-name].md
```

### For Agent 01 Build Fix
```
/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md
```

## Parameter Explanation

- **--persona-backend**: Specialized for backend development and build system fixes
- **--seq**: Sequential thinking for systematic problem-solving
- **@PRPs/active/fix-build-errors.md**: Reference to the generated PRP file

## Implementation Agent Context

The implementation agent will receive:
1. **Complete PRP**: All requirements, documentation, and validation commands
2. **Research Integration**: Links to all relevant research documentation
3. **Validation Framework**: Specific commands to run for validation
4. **Evidence Collection**: Requirements for systematic evidence gathering

## Process Flow

1. **PRP Generated** ✅ (Current Status)
   - Location: `PRPs/active/fix-build-errors.md`
   - Contains: Complete implementation guidance

2. **Launch Implementation Agent** (Next Step)
   - Command: `/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md`
   - Agent will read and execute the PRP

3. **Validation & Evidence** (During Execution)
   - Agent runs validation commands from PRP
   - Collects evidence in validation-results/
   - Updates orchestration trackers

4. **Completion** (Expected Outcome)
   - Build errors fixed
   - Evidence collected
   - Next agents unblocked

## Launch Instructions

### Step 1: Start New Agent Instance
```bash
# Launch new Claude instance for implementation
# Use the implementation agent context
```

### Step 2: Provide Execution Command
```
/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md
```

### Step 3: Monitor Progress
```bash
# Check agent status
/agent-status 01

# View orchestration tracker
cat .claudedocs/orchestration/analysis-engine-prod-tracker.md

# Check knowledge bank
cat .claude/memory/analysis-engine-prod-knowledge.json
```

## Success Indicators

- [ ] Build errors resolved (cargo build succeeds)
- [ ] Validation commands pass (clippy, tests)
- [ ] Evidence collected in validation-results/
- [ ] Orchestration trackers updated
- [ ] Next agents unblocked

## Recovery Commands

If the implementation agent needs context recovery:
- `/recover-analysis-engine` - Full orchestration context
- `/agent-status agent-01-build-fix` - Specific agent status

## Documentation Updates

After successful execution:
- Update orchestration tracker with completion
- Update knowledge bank with findings
- Prepare for next agent launch (Agents 02-04)

## Notes

- The implementation agent will work manually (no automation scripts)
- All changes will be research-backed using the provided documentation
- Validation loops will ensure quality before completion
- Evidence will be systematically collected for audit trail