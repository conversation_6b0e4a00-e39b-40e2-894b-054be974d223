# Orchestration Slash Command Reference

## PRP Commands

### Generate PRP
```
/generate-prp --persona-backend --seq --c7 --ultrathink @PRPs/active/fix-build-errors-INITIAL.md
```

### Execute PRP
```
/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md
```

## Recovery Commands

### Full Orchestration Recovery
```
/recover-analysis-engine
```

### Agent Status Check
```
/agent-status 01
/agent-status agent-01-build-fix
/agent-status all
```

### Sync Findings
```
/sync-findings
/sync-findings --format detailed
/sync-findings --output validation-results/sync-report.md
```

## Analysis Commands

### Analyze with Research
```
/analyze --code --security --seq --c7 @services/analysis-engine/build.rs
```

### Troubleshoot Issues
```
/troubleshoot --investigate --seq --evidence @services/analysis-engine/build.rs
```

## Build Commands

### Build Feature
```
/build --feature --backend --seq @PRPs/active/fix-build-errors.md
```

## Key Parameters

- **--persona-backend**: Backend development focus
- **--persona-architect**: System design focus
- **--persona-security**: Security analysis
- **--persona-performance**: Performance optimization
- **--seq**: Sequential thinking for complex analysis
- **--c7**: Context7 MCP for documentation lookup
- **--ultrathink**: Deep reasoning (32K tokens)
- **--think-hard**: Architectural analysis (10K tokens)
- **--think**: Multi-file analysis (4K tokens)

## File References

Always use `@` to reference files:
- `@.claudedocs/orchestration/agent-01-build-fix-tracker.md`
- `@PRPs/active/fix-build-errors-INITIAL.md`
- `@services/analysis-engine/build.rs`

## Agent-Specific Commands

### Agent 01 (Build Fix)
```
/generate-prp --persona-backend --seq --c7 --ultrathink @PRPs/active/fix-build-errors-INITIAL.md
/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md
```

### Agent 02 (Format Strings)
```
/generate-prp --persona-refactorer --seq @PRPs/active/format-strings-INITIAL.md
/execute-prp --persona-refactorer --seq @PRPs/active/format-strings.md
```

### Agent 03 (Code Patterns)
```
/generate-prp --persona-performance --seq @PRPs/active/code-patterns-INITIAL.md
/execute-prp --persona-performance --seq @PRPs/active/code-patterns.md
```

### Agent 04 (Code Structure)
```
/generate-prp --persona-architect --seq @PRPs/active/code-structure-INITIAL.md
/execute-prp --persona-architect --seq @PRPs/active/code-structure.md
```

### Agent 05 (Validation)
```
/analyze --code --quality --seq @services/analysis-engine/
/scan --validate --strict @validation-results/
```