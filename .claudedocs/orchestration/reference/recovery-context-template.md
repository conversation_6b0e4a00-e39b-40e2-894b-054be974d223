# RECOVERY_CONTEXT Standard Template

## Purpose
This template defines the standardized format for recovery metadata that must be included in all orchestration files. This enables the static `/recover-analysis-engine` command to restore full context without requiring command updates.

## Standard Format

Every orchestration file must include this exact section:

```markdown
## RECOVERY_CONTEXT
Phase: [1|2|3]
Progress: [0-100]%
Status: [🔴|🟡|🟢] [CRITICAL|HIGH|MEDIUM|LOW]
Active_Work: [Brief description of current work]
Next_Action: [Specific next step with file paths]
Blockers: [List of blockers or "None"]
Critical_Files: [List of essential files for recovery]
Quick_Commands: [List of relevant commands]
Evidence_Path: [Path to evidence/validation files]
Updated: [ISO timestamp]
```

## Field Definitions

### Phase
- **1**: Code Quality Resolution
- **2**: Production Assessment  
- **3**: Strategic Assessment

### Progress
- Percentage completion for current phase
- Format: `17%` (with percent symbol)

### Status
- **🔴 CRITICAL**: Blocking issues, immediate attention required
- **🟡 HIGH**: Important issues, high priority
- **🟡 MEDIUM**: Standard progress, medium priority
- **🟢 LOW**: Good progress, low risk

### Active_Work
- Brief description of current work in progress
- Examples: "Agent 07B verification", "Phase 2 sprint planning"

### Next_Action
- Specific, actionable next step
- Include file paths when relevant
- Examples: "Launch Agent 07B using prompt in active/agent-07b-verification/"

### Blockers
- List current blockers or "None"
- Examples: "Waiting for Agent 07B completion", "Missing research files"

### Critical_Files
- List of files essential for recovery
- Always include main tracker and relevant guides
- Examples: `["analysis-engine-prod-tracker.md", "active/PHASE2-GUIDE.md"]`

### Quick_Commands
- List of relevant commands for current state
- Examples: `["/agent-status 07b", "/sync-findings"]`

### Evidence_Path
- Path to validation/evidence files
- Examples: `"validation-results/phase2-assessment/"`

### Updated
- ISO timestamp when recovery context was last updated
- Format: `2025-01-16T19:30:00Z`

## Usage Guidelines

### When to Update
- After completing any agent work
- When changing phases
- When blockers are added/removed
- When critical files change
- At least daily during active work

### File Locations
This section should be included in:
- `analysis-engine-prod-tracker.md` (main tracker)
- `README.md` (overview)
- `active/PHASE2-GUIDE.md` (current phase guide)
- Active agent tracker files

### Machine Parsing
The recovery command will:
1. Search for `## RECOVERY_CONTEXT` sections
2. Parse each field using regex
3. Aggregate information across files
4. Present unified recovery report

## Examples

### Phase 1 Complete Example
```markdown
## RECOVERY_CONTEXT
Phase: 1
Progress: 100%
Status: 🟢 LOW
Active_Work: Phase 1 completed, Phase 2 ready
Next_Action: Launch Agent 07B using prompt in active/agent-07b-verification/
Blockers: None
Critical_Files: ["analysis-engine-prod-tracker.md", "active/PHASE2-GUIDE.md"]
Quick_Commands: ["/agent-status all", "/sync-findings"]
Evidence_Path: "validation-results/phase2-assessment/"
Updated: 2025-01-16T19:30:00Z
```

### Active Agent Example
```markdown
## RECOVERY_CONTEXT
Phase: 2
Progress: 17%
Status: 🟡 MEDIUM
Active_Work: Agent 07B independent PRP verification
Next_Action: Monitor Agent 07B execution and collect findings
Blockers: None
Critical_Files: ["agents/active/agent-07b-tracker.md", "active/agent-07b-verification/"]
Quick_Commands: ["/agent-status 07b", "/recover-analysis-engine --agent 07b"]
Evidence_Path: "validation-results/phase2-assessment/agent-07b-verification/"
Updated: 2025-01-16T20:15:00Z
```

### Blocked State Example
```markdown
## RECOVERY_CONTEXT
Phase: 2
Progress: 17%
Status: 🔴 CRITICAL
Active_Work: Agent 07B verification blocked
Next_Action: Resolve Agent 07B execution issues before proceeding
Blockers: ["Agent 07B failed to start", "Missing verification prompt"]
Critical_Files: ["active/agent-07b-verification/agent-07b-independent-verification-prompt.md"]
Quick_Commands: ["/troubleshoot agent-07b", "/recover-analysis-engine --status"]
Evidence_Path: "validation-results/phase2-assessment/"
Updated: 2025-01-16T21:00:00Z
```

## Integration with Recovery Command

The static `/recover-analysis-engine` command will:
1. Read all orchestration files
2. Extract RECOVERY_CONTEXT sections
3. Parse and aggregate information
4. Generate unified recovery report
5. Provide specific next actions

This ensures zero maintenance burden while providing comprehensive recovery capabilities.

---

**Standard Version**: 1.0  
**Created**: 2025-01-16  
**Purpose**: Enable maintenance-free context recovery for orchestration work