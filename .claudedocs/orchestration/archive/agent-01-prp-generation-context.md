# Agent 01: PRP Generation Agent - Build Fix Context

You are Agent 01 in the analysis-engine production readiness orchestration. Your role is to **generate a PRP** (not execute it) for fixing the critical build errors that are blocking all development.

## Your Mission

Create a comprehensive Product Requirements Prompt (PRP) for fixing the serde_json::Error::custom compilation errors in services/analysis-engine/build.rs.

## Step 1: Create INITIAL.md

Create the file at: `PRPs/active/fix-build-errors-INITIAL.md`

Use the template from `/INITIAL.md` as your guide. Here's the specific content for this task:

```markdown
# Fix Analysis-Engine Build Errors - INITIAL

## FEATURE:
Fix critical serde_json::Error::custom compilation errors in services/analysis-engine/build.rs that are blocking all development. The build system cannot compile due to missing trait imports and improper error construction. This is the highest priority as it blocks 11 other agents in the production readiness orchestration.

Specific errors:
- 3 instances of "no function or associated item named `custom` found for struct `serde_json::Error`" at lines 134, 142, 148
- 10 clippy errors about unwrap() and expect() usage in build.rs

## EXAMPLES:
- services/analysis-engine/build.rs - The file with compilation errors
- services/analysis-engine/after_clippy.txt - Shows all compilation errors
- validation-results/analysis-engine-prod-readiness/evidence/phase1-static-analysis/clippy-output-20250715_002624.txt - Historical validation
- examples/analysis-engine/error_handling.rs - Error handling patterns to follow

## DOCUMENTATION:

**Research Directory References (REQUIRED):**
- research/rust/rust-error-handling-overview.md - Rust error handling patterns
- research/rust/rust-recoverable-errors-result.md - Result type best practices
- research/rust/serde/serde-comprehensive.md - Serde error handling (if available)
- .claudedocs/methodology/CONTEXT_ENGINEERING_DEVELOPMENT_GUIDE.md - Development methodology
- .claudedocs/methodology/Super_Claude_Docs.md - SuperClaude configuration

**Current Project Context (CRITICAL):**
- .claudedocs/orchestration/analysis-engine-prod-tracker.md - Orchestration status
- .claudedocs/orchestration/agents/agent-01-build-fix-tracker.md - Agent tracking
- .claude/memory/analysis-engine-prod-knowledge.json - Knowledge bank
- validation-results/analysis-engine-prod-readiness/ANALYSIS_ENGINE_PRODUCTION_STATUS.md - Current status

**Official Documentation (Fresh Research Required):**
- https://docs.rs/serde/latest/serde/de/trait.Error.html - Serde Error trait
- https://serde.rs/error-handling.html - Official Serde error handling guide
- https://doc.rust-lang.org/book/ch09-02-recoverable-errors-with-result.html - Rust error handling

## OTHER CONSIDERATIONS:

**CRITICAL CONTEXT:**
- This is blocking ALL other development work
- 11 agents are waiting for this fix to proceed
- The entire orchestration depends on successful build

**Technical Requirements:**
- Must add proper trait import: `use serde::de::Error;`
- Replace `serde_json::Error::custom()` with proper trait method usage
- Fix all unwrap() calls with expect() and descriptive messages (acceptable in build.rs)
- Ensure no new warnings are introduced
- Maintain exact functionality while fixing errors

**Validation Requirements:**
- `cargo build` must succeed
- `cargo clippy -- -D warnings` must pass
- All tests must continue to pass
- Evidence must be collected in validation-results/

**Orchestration Integration:**
- Update tracker at .claudedocs/orchestration/agents/agent-01-build-fix-tracker.md
- Update knowledge bank at .claude/memory/analysis-engine-prod-knowledge.json
- Mark completion in main orchestration tracker
- Provide clear handoff notes for next agents

**Context Engineering Requirements:**
- No automation scripts - all fixes must be done manually
- Research-first approach using official documentation
- Systematic validation with evidence collection
- Self-correction loops until all tests pass
```

## Step 2: Generate the PRP

After creating the INITIAL.md file, the human orchestrator will run:
```
/generate-prp --persona-backend --seq --c7 --ultrathink @PRPs/active/fix-build-errors-INITIAL.md
```

This will create a comprehensive PRP at `PRPs/active/fix-build-errors.md`

## Important Guidelines

1. **Research Integration**: Include at least 5 specific research file references
2. **Comprehensive Context**: Provide complete understanding for the implementation agent
3. **Validation Commands**: Include specific commands for systematic validation
4. **No Placeholders**: Generate complete, actionable implementation guidance
5. **Evidence Requirements**: Specify exact evidence collection needs

## Context References

- **Template**: `/INITIAL.md` - Use this as your guide
- **Methodology**: `.claudedocs/methodology/CONTEXT_ENGINEERING_DEVELOPMENT_GUIDE.md`
- **SuperClaude**: `.claudedocs/methodology/Super_Claude_Docs.md`
- **Orchestration**: `.claudedocs/orchestration/analysis-engine-prod-tracker.md`

## Success Criteria

✅ INITIAL.md created with all required sections
✅ Research references included (minimum 5)
✅ Validation requirements specified
✅ Orchestration context integrated
✅ Ready for PRP generation command

Begin by reading the current build errors in `services/analysis-engine/build.rs` and `services/analysis-engine/after_clippy.txt` to understand the specific issues.