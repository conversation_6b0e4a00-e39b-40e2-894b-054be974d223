# Phase 2 Execution Quick Reference Guide

## 🚀 Sprint Kickoff Checklist

### Pre-Sprint Verification
```bash
# Verify Phase 1 completion
/agent-status all

# Check current repository state
cd /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine
cargo build --release  # Should succeed
cargo test            # 116 passing, 0 failing
cargo clippy          # 47 warnings (target <50 achieved)
```

## 📋 Agent Preparation Commands

### Day 1: Create INITIAL.md Files

#### Agent 07 - PRP Alignment
```bash
# Navigate to PRPs directory
cd /Users/<USER>/Documents/GitHub/episteme/PRPs/initial-files/

# Create INITIAL.md
cat > agent-07-prp-alignment-INITIAL.md << 'EOF'
[Agent 07 INITIAL.md content - to be created]
EOF
```

#### Agent 08 - Research Integration
```bash
cat > agent-08-research-integration-INITIAL.md << 'EOF'
[Agent 08 INITIAL.md content - to be created]
EOF
```

#### Agent 09 - Phase 4 Features
```bash
cat > agent-09-phase4-features-INITIAL.md << 'EOF'
[Agent 09 INITIAL.md content - to be created]
EOF
```

#### Agent 10 - Security Hardening
```bash
cat > agent-10-security-hardening-INITIAL.md << 'EOF'
[Agent 10 INITIAL.md content - to be created]
EOF
```

#### Agent 11 - Performance Validation
```bash
cat > agent-11-performance-validation-INITIAL.md << 'EOF'
[Agent 11 INITIAL.md content - to be created]
EOF
```

#### Agent 12 - Context Engineering
```bash
cat > agent-12-context-engineering-INITIAL.md << 'EOF'
[Agent 12 INITIAL.md content - to be created]
EOF
```

## 🎯 PRP Generation Commands

### Generate All PRPs (Day 1-2)
```bash
# Agent 07 - PRP Alignment
/generate-prp PRPs/initial-files/agent-07-prp-alignment-INITIAL.md \
  --persona-architect --seq --c7 --ultrathink

# Agent 08 - Research Integration  
/generate-prp PRPs/initial-files/agent-08-research-integration-INITIAL.md \
  --persona-analyzer --seq --c7 --ultrathink

# Agent 09 - Phase 4 Features
/generate-prp PRPs/initial-files/agent-09-phase4-features-INITIAL.md \
  --persona-backend --seq --c7 --ultrathink

# Agent 10 - Security Hardening
/generate-prp PRPs/initial-files/agent-10-security-hardening-INITIAL.md \
  --persona-security --seq --c7 --ultrathink

# Agent 11 - Performance Validation
/generate-prp PRPs/initial-files/agent-11-performance-validation-INITIAL.md \
  --persona-performance --seq --c7 --ultrathink

# Agent 12 - Context Engineering
/generate-prp PRPs/initial-files/agent-12-context-engineering-INITIAL.md \
  --persona-architect --seq --c7 --ultrathink
```

## 🏃 Agent Execution Commands

### Group 1: Methodology & Compliance (Day 2-3)
```bash
# Execute in parallel (separate terminals)

# Terminal 1 - Agent 07
/execute-prp PRPs/active/agent-07-prp-alignment.md \
  --persona-architect --seq --ultrathink

# Terminal 2 - Agent 08  
/execute-prp PRPs/active/agent-08-research-integration.md \
  --persona-analyzer --seq --ultrathink

# Terminal 3 - Agent 12
/execute-prp PRPs/active/agent-12-context-engineering.md \
  --persona-architect --seq --ultrathink
```

### Group 2: Technical Validation (Day 4-5)
```bash
# Execute in parallel (separate terminals)

# Terminal 1 - Agent 09
/execute-prp PRPs/active/agent-09-phase4-features.md \
  --persona-backend --seq --ultrathink

# Terminal 2 - Agent 10
/execute-prp PRPs/active/agent-10-security-hardening.md \
  --persona-security --seq --ultrathink

# Terminal 3 - Agent 11
/execute-prp PRPs/active/agent-11-performance-validation.md \
  --persona-performance --seq --ultrathink
```

## 📊 Monitoring Commands

### Daily Status Checks
```bash
# Check all agents
/agent-status all

# Check specific agent
/agent-status 07
/agent-status 08

# Sync findings
/sync-findings

# Recovery if needed
/recover-analysis-engine
```

### Evidence Verification
```bash
# Check evidence collection
ls -la validation-results/phase2-assessment/

# Review agent reports
find validation-results/phase2-assessment/ -name "*.md" -type f
```

## 🎯 Success Validation Checklist

### Per Agent Completion
- [ ] INITIAL.md created and archived
- [ ] PRP generated successfully
- [ ] Agent executed without errors
- [ ] Evidence collected in validation-results
- [ ] Findings documented
- [ ] Tracker updated

### Daily Checkpoints
- [ ] Morning: Review previous day's completions
- [ ] Midday: Check current agent progress
- [ ] Evening: Update orchestration tracker
- [ ] EOD: Prepare next day's agents

### Sprint Completion
- [ ] All 6 agents completed
- [ ] All evidence collected
- [ ] Phase 2 summary report created
- [ ] Knowledge bank updated
- [ ] Phase 3 preparation complete

## 🚨 Troubleshooting

### Common Issues & Solutions

#### Agent Fails to Start
```bash
# Check PRP exists
ls PRPs/active/agent-*.md

# Verify no syntax errors
cat PRPs/active/agent-XX-*.md | head -50

# Retry with recovery
/recover-analysis-engine
```

#### Evidence Not Collecting
```bash
# Create directory structure
mkdir -p validation-results/phase2-assessment/agent-{07..12}

# Check permissions
ls -la validation-results/phase2-assessment/
```

#### Conflicting Findings
1. Document both perspectives
2. Use UltraThink to reconcile
3. Human orchestrator decision if needed

## 📈 Progress Tracking

### Update Commands
```bash
# After each agent completes
1. Update orchestration tracker
2. Archive completed work
3. Update knowledge bank
4. Create checkpoint
```

### Sprint Metrics
- Target: 6 agents in 7 days
- Parallel execution: 3 agents at once
- Evidence per agent: 3-5 documents
- Final deliverable: Comprehensive assessment

## 🎉 Sprint Completion

### Final Steps
1. Create Phase 2 Summary Report
2. Archive all agent work
3. Update orchestration tracker to 100%
4. Prepare Phase 3 Agent 13 INITIAL.md
5. Celebrate achievement! 🚀

---

**Quick Start**: Begin with Agent 07 INITIAL.md creation  
**First Command**: Navigate to PRPs/initial-files/  
**Support**: Use `/recover-analysis-engine` if stuck