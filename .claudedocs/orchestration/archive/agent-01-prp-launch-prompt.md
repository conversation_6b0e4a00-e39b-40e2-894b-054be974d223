# Agent 01: Build Fix Agent - PRP-Based Serde Error Resolution

## Context Engineering Project Context

You are Agent 01 in a 12-agent orchestration to achieve production readiness for the analysis-engine service. Following Context Engineering methodology, you will use the PRP (Product Requirements Prompt) process:
1. Create an INITIAL.md file describing requirements
2. Generate a comprehensive PRP with research integration
3. Execute the PRP with validation loops

**CRITICAL**: The entire multi-agent orchestration is blocked by build errors. Your successful completion unlocks 11 other agents.

## Step 1: Create INITIAL.md

Create the file at: `PRPs/active/fix-build-errors-INITIAL.md`

```markdown
# INITIAL: Fix Analysis-Engine Build Errors

## FEATURE:
Fix critical serde_json::Error::custom compilation errors in services/analysis-engine/build.rs that are blocking all development. The build system cannot compile due to missing trait imports and improper error construction. This must be resolved to unblock 11 other agents in the production readiness orchestration.

## EXAMPLES:
- services/analysis-engine/build.rs - Contains 3 serde_json::Error::custom errors at lines 134, 142, 148
- services/analysis-engine/after_clippy.txt - Shows compilation errors and 10 unwrap/expect warnings
- validation-results/analysis-engine-prod-readiness/evidence/phase1-static-analysis/clippy-output-20250715_002624.txt - Historical evidence

## DOCUMENTATION:
- research/rust/rust-error-handling-overview.md - Rust error handling patterns
- research/rust/rust-recoverable-errors-result.md - Result type usage
- .claudedocs/methodology/CONTEXT_ENGINEERING_DEVELOPMENT_GUIDE.md - Development methodology
- .claudedocs/methodology/Super_Claude_Docs.md - SuperClaude configuration
- https://docs.rs/serde/latest/serde/de/trait.Error.html - Serde error trait documentation
- https://serde.rs/error-handling.html - Official Serde error handling guide

## OTHER CONSIDERATIONS:
- This is blocking 11 other agents in the orchestration
- Must maintain exact functionality while fixing errors
- build.rs is a build script, so expect() with descriptive messages is acceptable
- No automation scripts - all fixes must be done manually
- Evidence collection required in validation-results/
- Update orchestration tracking in .claudedocs/orchestration/
```

## Step 2: Generate PRP

Use the slash command:
```
/generate-prp --persona-backend --seq --c7 --ultrathink @PRPs/active/fix-build-errors-INITIAL.md
```

This will create a comprehensive PRP at `PRPs/active/fix-build-errors.md` with:
- Research integration (minimum 5 references)
- Systematic validation loops
- Evidence collection requirements
- Complete implementation guidance
- No placeholders or pseudo-code

## Step 3: Execute PRP

Use the slash command:
```
/execute-prp --persona-backend --seq @PRPs/active/fix-build-errors.md
```

Follow the PRP's validation loops:
1. Implement the fix
2. Run validation commands
3. Collect evidence
4. Self-correct if needed
5. Repeat until all tests pass

## Validation Requirements

Your PRP should include these validation commands:
```bash
# Must all succeed
cargo build                                    # Compilation must succeed
cargo clippy -- -D warnings                   # No clippy warnings
cargo test                                    # All tests must pass
```

## Orchestration Integration

Update these tracking files throughout your work:

1. **Agent Tracker**: `.claudedocs/orchestration/agents/agent-01-build-fix-tracker.md`
   - Mark tasks as completed
   - Add evidence file paths
   - Document findings
   - Add handoff notes

2. **Knowledge Bank**: `.claude/memory/analysis-engine-prod-knowledge.json`
   - Update agent-01 status
   - Add completed tasks
   - Record evidence locations

3. **Main Tracker**: `.claudedocs/orchestration/analysis-engine-prod-tracker.md`
   - Update overall progress percentage
   - Note completion of Agent 01

## Evidence Collection

Create evidence files in:
```
validation-results/analysis-engine-prod-readiness/evidence/agent-01/
├── initial-errors.txt          # Original build errors
├── serde-research.md          # Research findings
├── fix-implementation.patch   # Git diff of changes
├── compilation-success.txt    # Successful build output
└── validation-complete.md     # Summary of validation
```

## Recovery Commands

If you lose context:
- `/recover-analysis-engine` - Full orchestration context
- `/agent-status agent-01-build-fix` - Your specific status
- Review: `.claudedocs/orchestration/agent-01-build-fix-tracker.md`

## Success Criteria

1. ✅ INITIAL.md created with comprehensive requirements
2. ✅ PRP generated with research integration
3. ✅ All serde_json::Error::custom errors fixed
4. ✅ All unwrap/expect warnings resolved
5. ✅ Successful compilation (cargo build passes)
6. ✅ Evidence collected in validation-results/
7. ✅ Orchestration trackers updated
8. ✅ Next agents unblocked

## Important Notes

- Follow Context Engineering research-first approach
- Use Context7 MCP (--c7) to fetch official documentation
- NO automation scripts - all edits must be manual
- Maintain exact functionality while fixing errors
- Document all decisions with research references

Begin by creating the INITIAL.md file, then generate and execute your PRP.