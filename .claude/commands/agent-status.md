# /agent-status

Shows the status and progress of Analysis-Engine production readiness agents.

## Usage
```
/agent-status [agent-id|all]
```

## Arguments
- `agent-id`: Specific agent ID (e.g., `01`, `build-fix`, `agent-01-build-fix`)
- `all`: Show status of all agents (default)

## Description
Displays detailed status information for orchestration agents including:
- Current task and progress
- Files modified or analyzed
- Evidence collected
- Blockers or dependencies
- Next steps for the agent

## Agent IDs
| ID | Name | Purpose |
|----|------|---------|
| 01 | Build Fix | Fix serde_json compilation errors |
| 02 | Format String | Modernize format strings |
| 03 | Code Pattern | Fix clamp patterns and casts |
| 04 | Code Structure | Refactor complex functions |
| 05 | Validation | Continuous clippy validation |
| 06 | PRP Alignment | Verify PRP compliance |
| 07 | Research Integration | Validate research usage |
| 08 | Phase 4 Features | Check feature compliance |
| 09 | Security | Deep security analysis |
| 10 | Performance | Performance validation |
| 11 | Context Engineering | CE compliance check |
| 12 | Process Evaluation | Strategic assessment |

## Example Usage
```
/agent-status 01
/agent-status build-fix
/agent-status all
```

## Example Output
```
Agent 01: Build Fix Agent
========================
Status: IN PROGRESS (75%)
Started: 2025-01-16 10:30
Mission: Fix serde_json::Error::custom compilation errors

Tasks:
✅ Analyze build.rs errors
✅ Research serde error handling patterns
🔄 Add required trait imports
⏳ Fix unwrap/expect usage

Files Modified:
- services/analysis-engine/build.rs (3 changes)

Evidence:
- Initial error analysis: evidence/agent-01/build-errors.txt
- Research findings: evidence/agent-01/serde-patterns.md

Current Blocker: None
Next Step: Test compilation after fixes
```

## Related Commands
- `/recover-analysis-engine` - Full orchestration recovery
- `/sync-findings` - Aggregate all agent findings