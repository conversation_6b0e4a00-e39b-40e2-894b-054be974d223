src/metrics/granular.rs:    pub authentication_errors: u64,
src/metrics/granular.rs:            "auth" => metrics.authentication_errors += 1,
src/bin/load_test.rs:#[command(author, version, about = "Load testing for analysis engine", long_about = None)]
src/auth/mod.rs:use google_cloud_auth::token::DefaultTokenSourceProvider;
src/auth/mod.rs:use google_cloud_auth::project::Config as ProjectConfig;
src/auth/mod.rs:/// Create a token source for Google Cloud authentication
src/auth/mod.rs:/// This will automatically use the appropriate authentication method:
src/auth/mod.rs:/// - User credentials if running locally with gcloud auth
src/auth/mod.rs:    // Use the default token source provider which handles all auth methods
src/auth/mod.rs:            "https://www.googleapis.com/auth/spanner.data",
src/auth/mod.rs:            "https://www.googleapis.com/auth/devstorage.read_write",
src/auth/mod.rs:            "https://www.googleapis.com/auth/pubsub",
src/auth/mod.rs:            "https://www.googleapis.com/auth/cloud-platform",
src/config.rs:    pub enable_auth: bool,
src/config.rs:    pub jwt_secret: Option<String>,
src/config.rs:                enable_auth: env::var("ENABLE_AUTH")
src/config.rs:                jwt_secret: env::var("JWT_SECRET").ok(),
src/config.rs:                    enable_auth: false,
src/config.rs:                    jwt_secret: None,
src/models/security.rs:            VulnerabilityType::BrokenAuthentication => "broken_authentication",
