// JWT Middleware Implementation Evidence
// From: services/analysis-engine/src/api/auth_extractor.rs
// Lines: 834 total - comprehensive production-grade implementation

/// JWT authentication implementation showing the code is NOT commented out
/// This directly contradicts the PRP claim that JWT is "commented out"

// Key evidence showing full JWT implementation:

// 1. JWT Claims Structure (lines 164-188)
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct JwtClaims {
    pub sub: String,           // Subject (user ID)
    pub aud: Vec<String>,      // Audience
    pub exp: usize,           // Expiration time
    pub iat: usize,           // Issued at
    pub nbf: Option<usize>,   // Not before
    pub iss: String,          // Issuer
    pub jti: Option<String>,  // JWT ID
    pub device_id: Option<String>,  // Device binding
    pub scopes: Vec<String>,  // Permission scopes
    pub user_type: String,    // User type (standard, admin, service)
}

// 2. JWT Validation Implementation (lines 386-465)
async fn validate_jwt_token(
    &self,
    token: &str,
    state: &Arc<AppState>,
) -> Result<AuthUser, AuthError> {
    // Comprehensive JWT validation with:
    // - Audience validation
    // - Expiration checking
    // - Device binding
    // - Scope validation
    // - Database user lookup
}

// 3. Tower Service Implementation (lines 674-742)
impl<S> Service<Request<Body>> for AuthService<S>
where
    S: Service<Request<Body>, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    // Full Tower middleware implementation
    // Handles JWT extraction and validation
    // NOT commented out - fully functional
}

// 4. Security Features (throughout file)
- JWT signature validation
- Token expiration checking
- Audience validation
- Device binding for enhanced security
- Scope-based authorization
- Audit logging of auth events
- Rate limiting integration

// CONCLUSION: JWT is FULLY IMPLEMENTED, not commented out as PRP claims