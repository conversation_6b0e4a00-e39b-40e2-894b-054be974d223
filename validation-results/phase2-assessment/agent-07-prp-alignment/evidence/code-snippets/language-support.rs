// Language Support Implementation Evidence
// From: services/analysis-engine/src/parser/unsafe_bindings.rs
// Shows 31 languages implemented vs 18+ documented

/// ACTUAL LANGUAGE SUPPORT: 31 LANGUAGES (not 18+)
/// This is 172% of the documented requirement

// Language Registry (lines 32-66)
static SUPPORTED_LANGUAGE_SET: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("erlang", "tree_sitter_erlang");
    map.insert("scala", "tree_sitter_scala");
    map.insert("rust", "tree_sitter_rust");
    map.insert("ocaml", "tree_sitter_ocaml");
    map.insert("html", "tree_sitter_html");
    map.insert("go", "tree_sitter_go");
    map.insert("bash", "tree_sitter_bash");
    map.insert("c", "tree_sitter_c");
    map.insert("xml", "tree_sitter_xml");
    map.insert("php", "tree_sitter_php");
    map.insert("typescript", "tree_sitter_typescript");
    map.insert("elixir", "tree_sitter_elixir");
    map.insert("d", "tree_sitter_d");
    map.insert("swift", "tree_sitter_swift");
    map.insert("ruby", "tree_sitter_ruby");
    map.insert("nix", "tree_sitter_nix");
    map.insert("md", "tree_sitter_markdown");
    map.insert("java", "tree_sitter_java");
    map.insert("julia", "tree_sitter_julia");
    map.insert("css", "tree_sitter_css");
    map.insert("javascript", "tree_sitter_javascript");
    map.insert("json", "tree_sitter_json");
    map.insert("haskell", "tree_sitter_haskell");
    map.insert("kotlin", "tree_sitter_kotlin");
    map.insert("objc", "tree_sitter_objc");
    map.insert("r", "tree_sitter_r");
    map.insert("cpp", "tree_sitter_cpp");
    map.insert("lua", "tree_sitter_lua");
    map.insert("yaml", "tree_sitter_yaml");
    map.insert("zig", "tree_sitter_zig");
    map.insert("python", "tree_sitter_python");
    map
});

// Test validation (line 226)
assert_eq!(languages.len(), 31); // Confirms 31 languages

// Language endpoint issue - hardcoded list (handlers/analysis.rs:580-611)
// Returns only 15 languages instead of using the registry:
let tree_sitter_languages = vec![
    "rust", "javascript", "typescript", "python", "go",
    "java", "c", "cpp", "html", "css", "json", "yaml",
    "ruby", "bash", "markdown",
]; // Missing 16 languages!

// EVIDENCE OF DISCREPANCY:
// - Implementation: 31 languages (unsafe_bindings.rs)
// - API endpoint: 15 languages (handlers/analysis.rs)
// - Documentation: 18+ languages (analysis-engine.md)
// 
// The service EXCEEDS requirements but API doesn't expose full capabilities