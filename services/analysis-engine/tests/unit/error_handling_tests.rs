use analysis_engine::models::*;
use analysis_engine::parser::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseErrorType};
use analysis_engine::api::errors::{ErrorResponse, ErrorType};
use analysis_engine::config::ServiceConfig;
use std::path::Path;
use std::sync::Arc;
use tempfile::TempDir;

#[tokio::test]
async fn test_parse_error_creation() {
    let error = ParseError {
        file_path: "test.rs".to_string(),
        error_type: ParseErrorType::ParseError,
        message: "Failed to parse file".to_string(),
        position: Some(Position { line: 10, column: 5, byte: 100 }),
    };
    
    assert_eq!(error.file_path, "test.rs");
    assert_eq!(error.error_type, ParseErrorType::ParseError);
    assert!(!error.message.is_empty());
    assert!(error.position.is_some());
}

#[tokio::test]
async fn test_parse_error_types() {
    let error_types = vec![
        ParseErrorType::ParseError,
        ParseErrorType::UnsupportedLanguage,
        ParseErrorType::FileTooLarge,
        ParseErrorType::Timeout,
        ParseErrorType::Other,
    ];
    
    for error_type in error_types {
        let error = ParseError {
            file_path: "test.rs".to_string(),
            error_type: error_type.clone(),
            message: "Test error".to_string(),
            position: None,
        };
        
        assert_eq!(error.error_type, error_type);
    }
}

#[tokio::test]
async fn test_file_not_found_error() {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();
    let result = parser.parse_file(Path::new("/nonexistent/file.rs")).await;
    
    assert!(result.is_err());
    let error = result.unwrap_err();
    assert_eq!(error.error_type, ParseErrorType::Other);
    assert!(error.message.contains("Failed to get file metadata") || error.message.contains("No such file"));
}

#[tokio::test]
async fn test_unsupported_language_error() {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();
    let temp_dir = TempDir::new().unwrap();
    let file_path = temp_dir.path().join("test.unknown");
    std::fs::write(&file_path, "some content").unwrap();
    
    let result = parser.parse_file(&file_path).await;
    assert!(result.is_err());
    
    let error = result.unwrap_err();
    assert_eq!(error.error_type, ParseErrorType::UnsupportedLanguage);
}

#[tokio::test]
async fn test_file_too_large_error() {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();
    let temp_dir = TempDir::new().unwrap();
    let file_path = temp_dir.path().join("large.rs");
    
    // Create a file that's too large
    let large_content = "// ".repeat(1_000_000); // 2MB+ file
    std::fs::write(&file_path, large_content).unwrap();
    
    let config = analysis_engine::parser::StreamingConfig {
        max_file_size: 1024, // 1KB limit
        ..Default::default()
    };
    
    let result = parser.parse_file_with_config(&file_path, &config).await;
    assert!(result.is_err());
    
    let error = result.unwrap_err();
    assert_eq!(error.error_type, ParseErrorType::FileTooLarge);
}

#[tokio::test]
async fn test_error_response_creation() {
    let error_response = ErrorResponse::validation_error("Invalid input".to_string());
    
    assert_eq!(error_response.error_type, ErrorType::ValidationError);
    assert!(!error_response.message.is_empty());
    assert!(error_response.timestamp > 0);
}

#[tokio::test]
async fn test_error_response_types() {
    let validation_error = ErrorResponse::validation_error("Validation failed".to_string());
    assert_eq!(validation_error.error_type, ErrorType::ValidationError);
    
    let internal_error = ErrorResponse::internal_error("Internal error".to_string());
    assert_eq!(internal_error.error_type, ErrorType::InternalError);
    
    let not_found_error = ErrorResponse::not_found_error("Resource not found".to_string());
    assert_eq!(not_found_error.error_type, ErrorType::NotFound);
    
    let rate_limit_error = ErrorResponse::rate_limit_error("Rate limit exceeded".to_string(), Some(60));
    assert_eq!(rate_limit_error.error_type, ErrorType::RateLimitExceeded);
}

#[tokio::test]
async fn test_analysis_warning_creation() {
    let warning = AnalysisWarning {
        warning_type: WarningType::ParseError,
        severity: WarningSeverity::High,
        message: "Parse error occurred".to_string(),
        file_path: Some("src/main.rs".to_string()),
        line_number: Some(42),
        column_number: Some(10),
        context: None,
    };
    
    assert_eq!(warning.warning_type, WarningType::ParseError);
    assert_eq!(warning.severity, WarningSeverity::High);
    assert!(!warning.message.is_empty());
}

#[tokio::test]
async fn test_warning_severity_levels() {
    let severities = vec![
        WarningSeverity::Low,
        WarningSeverity::Medium,
        WarningSeverity::High,
        WarningSeverity::Critical,
    ];
    
    for severity in severities {
        let warning = AnalysisWarning {
            warning_type: WarningType::ParseError,
            severity: severity.clone(),
            message: "Test warning".to_string(),
            file_path: None,
            line_number: None,
            column_number: None,
            context: None,
        };
        
        assert_eq!(warning.severity, severity);
    }
}

#[tokio::test]
async fn test_warning_types() {
    let warning_types = vec![
        WarningType::ParseError,
        WarningType::MemoryLimit,
        WarningType::FileSizeLimit,
        WarningType::TimeoutWarning,
        WarningType::UnsupportedFeature,
        WarningType::PerformanceIssue,
    ];
    
    for warning_type in warning_types {
        let warning = AnalysisWarning {
            warning_type: warning_type.clone(),
            severity: WarningSeverity::Medium,
            message: "Test warning".to_string(),
            file_path: None,
            line_number: None,
            column_number: None,
            context: None,
        };
        
        assert_eq!(warning.warning_type, warning_type);
    }
}

#[tokio::test]
async fn test_failed_file_creation() {
    let failed_file = FailedFile {
        file_path: "src/broken.rs".to_string(),
        error_message: "Syntax error".to_string(),
        error_type: FileErrorType::ParseError,
    };
    
    assert_eq!(failed_file.file_path, "src/broken.rs");
    assert_eq!(failed_file.error_type, FileErrorType::ParseError);
    assert!(!failed_file.error_message.is_empty());
}

#[tokio::test]
async fn test_file_error_types() {
    let error_types = vec![
        FileErrorType::ParseError,
        FileErrorType::UnsupportedLanguage,
        FileErrorType::FileTooLarge,
        FileErrorType::Timeout,
    ];
    
    for error_type in error_types {
        let failed_file = FailedFile {
            file_path: "test.rs".to_string(),
            error_message: "Test error".to_string(),
            error_type: error_type.clone(),
        };
        
        assert_eq!(failed_file.error_type, error_type);
    }
}

#[tokio::test]
async fn test_error_serialization() {
    let error_response = ErrorResponse::validation_error("Test error".to_string());
    
    // Test JSON serialization
    let json_result = serde_json::to_string(&error_response);
    assert!(json_result.is_ok());
    
    let json_str = json_result.unwrap();
    assert!(!json_str.is_empty());
    assert!(json_str.contains("ValidationError"));
    assert!(json_str.contains("Test error"));
    
    // Test deserialization
    let deserialized: Result<ErrorResponse, _> = serde_json::from_str(&json_str);
    assert!(deserialized.is_ok());
    
    let deserialized_error = deserialized.unwrap();
    assert_eq!(deserialized_error.error_type, ErrorType::ValidationError);
    assert_eq!(deserialized_error.message, "Test error");
}

#[tokio::test]
async fn test_graceful_error_handling() {
    // Test that the system handles various error conditions gracefully
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();
    
    // Test with empty file
    let temp_dir = TempDir::new().unwrap();
    let empty_file = temp_dir.path().join("empty.rs");
    std::fs::write(&empty_file, "").unwrap();
    
    let result = parser.parse_file(&empty_file).await;
    // Should handle empty files gracefully
    assert!(result.is_ok() || matches!(result.unwrap_err().error_type, ParseErrorType::ParseError));
    
    // Test with binary file
    let binary_file = temp_dir.path().join("binary.rs");
    let binary_content = vec![0u8, 1u8, 2u8, 255u8];
    std::fs::write(&binary_file, binary_content).unwrap();
    
    let result = parser.parse_file(&binary_file).await;
    // Should handle binary files gracefully
    assert!(result.is_ok() || result.is_err());
}

#[tokio::test]
async fn test_error_context_preservation() {
    let parse_error = ParseError {
        file_path: "src/main.rs".to_string(),
        error_type: ParseErrorType::ParseError,
        message: "Unexpected token".to_string(),
        position: Some(Position { line: 42, column: 10, byte: 500 }),
    };
    
    // Test that error context is preserved
    assert_eq!(parse_error.file_path, "src/main.rs");
    assert!(parse_error.position.is_some());
    
    let position = parse_error.position.unwrap();
    assert_eq!(position.line, 42);
    assert_eq!(position.column, 10);
    assert_eq!(position.byte, 500);
}
