use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use walkdir::WalkDir;

#[derive(Debug, Serialize, Deserialize)]
struct RepositoryStats {
    name: String,
    total_files: usize,
    total_loc: usize,
    languages: HashMap<String, usize>,
    meets_1m_requirement: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct RepositoryMetadata {
    name: String,
    url: String,
    loc_count: usize,
    timestamp: String,
    meets_1m_requirement: bool,
}

/// Validate that test repositories meet our 1M LOC requirement
async fn validate_test_repositories() -> Result<Vec<RepositoryStats>> {
    let repo_dir = Path::new("test-data/large-repositories");
    let mut results = Vec::new();

    if !repo_dir.exists() {
        return Err(anyhow::anyhow!(
            "Test repository directory not found. Run collect-test-repositories.sh first."
        ));
    }

    for entry in fs::read_dir(repo_dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_dir() && !path.file_name().unwrap().to_string_lossy().starts_with('.') {
            let metadata_path = path.with_extension("").with_extension("metadata.json");
            
            if metadata_path.exists() {
                // Load existing metadata
                let metadata_content = fs::read_to_string(&metadata_path)?;
                let metadata: RepositoryMetadata = serde_json::from_str(&metadata_content)?;
                
                // Perform detailed analysis
                let stats = analyze_repository(&path).await?;
                results.push(stats);
            }
        }
    }

    // Sort by LOC descending
    results.sort_by(|a, b| b.total_loc.cmp(&a.total_loc));

    Ok(results)
}

async fn analyze_repository(path: &Path) -> Result<RepositoryStats> {
    let mut stats = RepositoryStats {
        name: path.file_name().unwrap().to_string_lossy().to_string(),
        total_files: 0,
        total_loc: 0,
        languages: HashMap::new(),
        meets_1m_requirement: false,
    };

    // Supported extensions from analysis-engine
    let supported_extensions = vec![
        "rs", "py", "go", "js", "ts", "tsx", "jsx", "java", "cpp", "c", "cc", "cxx",
        "h", "hpp", "rb", "php", "cs", "swift", "kt", "m", "mm", "scala", "r", "jl",
        "lua", "pl", "sh", "bash", "zsh", "fish", "yaml", "yml", "json", "toml",
    ];

    for entry in WalkDir::new(path)
        .follow_links(false)
        .into_iter()
        .filter_entry(|e| !is_excluded_path(e.path()))
    {
        let entry = entry?;
        if entry.file_type().is_file() {
            if let Some(extension) = entry.path().extension() {
                let ext = extension.to_string_lossy().to_lowercase();
                if supported_extensions.contains(&ext.as_str()) {
                    stats.total_files += 1;
                    
                    match count_lines_in_file(entry.path()).await {
                        Ok(loc) => {
                            stats.total_loc += loc;
                            *stats.languages.entry(ext.clone()).or_insert(0) += loc;
                        }
                        Err(e) => {
                            log::warn!("Failed to count lines in {:?}: {}", entry.path(), e);
                        }
                    }
                }
            }
        }
    }

    stats.meets_1m_requirement = stats.total_loc >= 1_000_000;

    Ok(stats)
}

fn is_excluded_path(path: &Path) -> bool {
    // Exclude common non-source directories
    let excluded_dirs = vec![
        ".git", "node_modules", "target", "build", "dist", "out", 
        ".idea", ".vscode", "vendor", "third_party", "__pycache__",
        ".pytest_cache", ".mypy_cache", "coverage", ".coverage"
    ];

    path.components().any(|component| {
        if let Some(name) = component.as_os_str().to_str() {
            excluded_dirs.contains(&name)
        } else {
            false
        }
    })
}

async fn count_lines_in_file(path: &Path) -> Result<usize> {
    let content = tokio::fs::read_to_string(path).await?;
    let non_empty_lines = content
        .lines()
        .filter(|line| !line.trim().is_empty())
        .count();
    Ok(non_empty_lines)
}

fn format_size(bytes: usize) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    format!("{:.2} {}", size, UNITS[unit_index])
}

#[tokio::test]
async fn test_repository_size_validation() {
    let repos = validate_test_repositories().await.unwrap();

    println!("\n📊 Repository Validation Results");
    println!("================================\n");

    for repo in &repos {
        println!("Repository: {}", repo.name);
        println!("  Total LOC: {:>12} {}", 
            repo.total_loc,
            if repo.meets_1m_requirement { "✅" } else { "❌" }
        );
        println!("  Total Files: {:>10}", repo.total_files);
        
        // Top 3 languages by LOC
        let mut languages: Vec<_> = repo.languages.iter().collect();
        languages.sort_by(|a, b| b.1.cmp(a.1));
        
        println!("  Top Languages:");
        for (lang, loc) in languages.iter().take(3) {
            println!("    - {:<10} {:>10} lines", lang, loc);
        }
        
        println!();
    }

    // Summary statistics
    let total_loc: usize = repos.iter().map(|r| r.total_loc).sum();
    let meeting_requirement = repos.iter().filter(|r| r.meets_1m_requirement).count();
    
    println!("Summary:");
    println!("  Total Repositories: {}", repos.len());
    println!("  Meeting 1M LOC: {}/{}", meeting_requirement, repos.len());
    println!("  Combined LOC: {}", total_loc);
    
    // Assert we have at least one repository meeting requirements
    assert!(
        meeting_requirement > 0,
        "No repositories meet the 1M LOC requirement for performance validation"
    );
}

#[tokio::test]
async fn test_repository_language_diversity() {
    let repos = validate_test_repositories().await.unwrap();
    
    // Collect all unique languages
    let mut all_languages = HashMap::new();
    for repo in &repos {
        for (lang, loc) in &repo.languages {
            *all_languages.entry(lang.clone()).or_insert(0) += loc;
        }
    }
    
    println!("\n🌐 Language Diversity Analysis");
    println!("==============================\n");
    
    let mut languages: Vec<_> = all_languages.iter().collect();
    languages.sort_by(|a, b| b.1.cmp(a.1));
    
    for (lang, total_loc) in languages.iter().take(15) {
        println!("{:<15} {:>15} lines", lang, total_loc);
    }
    
    println!("\nTotal Languages: {}", all_languages.len());
    
    // Ensure we have diverse language coverage
    assert!(all_languages.len() >= 5, "Insufficient language diversity for testing");
}

#[tokio::test]
async fn test_individual_repository_characteristics() {
    let repos = validate_test_repositories().await.unwrap();
    
    println!("\n🔍 Individual Repository Characteristics");
    println!("=======================================\n");
    
    for repo in &repos {
        if repo.meets_1m_requirement {
            let avg_lines_per_file = if repo.total_files > 0 {
                repo.total_loc / repo.total_files
            } else {
                0
            };
            
            println!("Repository: {}", repo.name);
            println!("  Average lines/file: {}", avg_lines_per_file);
            println!("  Language count: {}", repo.languages.len());
            
            // Check for potential memory issues
            if avg_lines_per_file > 10000 {
                println!("  ⚠️  Warning: High average lines per file");
            }
            
            if repo.total_loc > 5_000_000 {
                println!("  ⚠️  Warning: Very large repository (>5M LOC)");
            }
            
            println!();
        }
    }
}