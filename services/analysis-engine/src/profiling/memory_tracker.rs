//! Memory tracking for performance validation
//! Agent 11B - Emergency Performance Validation

use std::alloc::{GlobalAlloc, Layout, System};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::Instant;

/// Tracks memory allocation and deallocation with peak usage monitoring
#[derive(Debug)]
pub struct MemoryTracker {
    allocated: AtomicUsize,
    deallocated: AtomicUsize,
    peak_usage: AtomicUsize,
    start_time: Instant,
}

impl MemoryTracker {
    /// Create a new memory tracker
    pub fn new() -> Self {
        Self {
            allocated: AtomicUsize::new(0),
            deallocated: AtomicUsize::new(0),
            peak_usage: AtomicUsize::new(0),
            start_time: Instant::now(),
        }
    }

    /// Track a memory allocation
    pub fn track_allocation(&self, size: usize) {
        let total_allocated = self.allocated.fetch_add(size, Ordering::SeqCst) + size;
        let current_usage = total_allocated.saturating_sub(self.deallocated.load(Ordering::SeqCst));
        
        // Update peak usage if current is higher
        self.peak_usage.fetch_max(current_usage, Ordering::SeqCst);
    }

    /// Track a memory deallocation
    pub fn track_deallocation(&self, size: usize) {
        self.deallocated.fetch_add(size, Ordering::SeqCst);
    }

    /// Get current memory statistics
    pub fn get_stats(&self) -> MemoryStats {
        let total_allocated = self.allocated.load(Ordering::SeqCst);
        let total_deallocated = self.deallocated.load(Ordering::SeqCst);
        
        MemoryStats {
            total_allocated,
            total_deallocated,
            current_usage: total_allocated.saturating_sub(total_deallocated),
            peak_usage: self.peak_usage.load(Ordering::SeqCst),
            duration: self.start_time.elapsed(),
        }
    }

    /// Reset the tracker to initial state
    pub fn reset(&self) {
        self.allocated.store(0, Ordering::SeqCst);
        self.deallocated.store(0, Ordering::SeqCst);
        self.peak_usage.store(0, Ordering::SeqCst);
    }
}

impl Default for MemoryTracker {
    fn default() -> Self {
        Self::new()
    }
}

/// Memory usage statistics
#[derive(Debug, Clone)]
pub struct MemoryStats {
    pub total_allocated: usize,
    pub total_deallocated: usize,
    pub current_usage: usize,
    pub peak_usage: usize,
    pub duration: std::time::Duration,
}

impl MemoryStats {
    /// Get peak memory usage in megabytes
    pub fn peak_mb(&self) -> f64 {
        self.peak_usage as f64 / 1024.0 / 1024.0
    }

    /// Get current memory usage in megabytes
    pub fn current_mb(&self) -> f64 {
        self.current_usage as f64 / 1024.0 / 1024.0
    }

    /// Get total allocated memory in megabytes
    pub fn total_allocated_mb(&self) -> f64 {
        self.total_allocated as f64 / 1024.0 / 1024.0
    }

    /// Check if memory usage is within Cloud Run limits (4GB)
    pub fn is_within_cloud_run_limit(&self) -> bool {
        self.peak_mb() < 4096.0
    }

    /// Get memory efficiency ratio (deallocated/allocated)
    pub fn efficiency_ratio(&self) -> f64 {
        if self.total_allocated == 0 {
            1.0
        } else {
            self.total_deallocated as f64 / self.total_allocated as f64
        }
    }

    /// Format memory statistics for logging
    pub fn format_summary(&self) -> String {
        format!(
            "Memory Stats - Peak: {:.2} MB, Current: {:.2} MB, Total Allocated: {:.2} MB, Efficiency: {:.2}%, Duration: {:.2}s",
            self.peak_mb(),
            self.current_mb(),
            self.total_allocated_mb(),
            self.efficiency_ratio() * 100.0,
            self.duration.as_secs_f64()
        )
    }
}

/// Global allocator wrapper for memory tracking
pub struct TrackingAllocator<A: GlobalAlloc> {
    inner: A,
    tracker: Arc<MemoryTracker>,
}

impl<A: GlobalAlloc> TrackingAllocator<A> {
    /// Create a new tracking allocator
    pub const fn new(inner: A, tracker: Arc<MemoryTracker>) -> Self {
        Self { inner, tracker }
    }

    /// Get a reference to the memory tracker
    pub fn tracker(&self) -> &Arc<MemoryTracker> {
        &self.tracker
    }
}

unsafe impl<A: GlobalAlloc> GlobalAlloc for TrackingAllocator<A> {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        let ptr = self.inner.alloc(layout);
        if !ptr.is_null() {
            self.tracker.track_allocation(layout.size());
        }
        ptr
    }

    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        self.inner.dealloc(ptr, layout);
        self.tracker.track_deallocation(layout.size());
    }

    unsafe fn alloc_zeroed(&self, layout: Layout) -> *mut u8 {
        let ptr = self.inner.alloc_zeroed(layout);
        if !ptr.is_null() {
            self.tracker.track_allocation(layout.size());
        }
        ptr
    }

    unsafe fn realloc(&self, ptr: *mut u8, layout: Layout, new_size: usize) -> *mut u8 {
        let new_ptr = self.inner.realloc(ptr, layout, new_size);
        if !new_ptr.is_null() {
            // Track deallocation of old size and allocation of new size
            self.tracker.track_deallocation(layout.size());
            self.tracker.track_allocation(new_size);
        }
        new_ptr
    }
}

thread_local! {
    static LOCAL_TRACKER: Arc<MemoryTracker> = Arc::new(MemoryTracker::new());
}

/// Get the thread-local memory tracker
pub fn local_tracker() -> Arc<MemoryTracker> {
    LOCAL_TRACKER.with(|tracker| tracker.clone())
}

/// Measure memory usage of a closure
pub fn measure_memory<F, R>(f: F) -> (R, MemoryStats)
where
    F: FnOnce() -> R,
{
    let tracker = MemoryTracker::new();
    let _allocator = TrackingAllocator::new(System, Arc::new(tracker));
    
    // Note: In a real implementation, we would temporarily install this allocator
    // For now, we'll use the local tracker approach
    let local = local_tracker();
    local.reset();
    
    let result = f();
    let stats = local.get_stats();
    
    (result, stats)
}

/// Async version of memory measurement
pub async fn measure_memory_async<F, Fut, R>(f: F) -> (R, MemoryStats)
where
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = R>,
{
    let local = local_tracker();
    local.reset();
    
    let result = f().await;
    let stats = local.get_stats();
    
    (result, stats)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_memory_tracker_basic() {
        let tracker = MemoryTracker::new();
        
        // Track some allocations
        tracker.track_allocation(1024);
        tracker.track_allocation(2048);
        
        let stats = tracker.get_stats();
        assert_eq!(stats.total_allocated, 3072);
        assert_eq!(stats.current_usage, 3072);
        assert_eq!(stats.peak_usage, 3072);
        
        // Track deallocation
        tracker.track_deallocation(1024);
        
        let stats = tracker.get_stats();
        assert_eq!(stats.current_usage, 2048);
        assert_eq!(stats.peak_usage, 3072); // Peak remains unchanged
    }

    #[test]
    fn test_memory_stats_formatting() {
        let stats = MemoryStats {
            total_allocated: 1024 * 1024 * 100, // 100 MB
            total_deallocated: 1024 * 1024 * 80, // 80 MB
            current_usage: 1024 * 1024 * 20, // 20 MB
            peak_usage: 1024 * 1024 * 50, // 50 MB
            duration: std::time::Duration::from_secs(10),
        };
        
        assert_eq!(stats.peak_mb(), 50.0);
        assert_eq!(stats.current_mb(), 20.0);
        assert_eq!(stats.total_allocated_mb(), 100.0);
        assert_eq!(stats.efficiency_ratio(), 0.8);
        assert!(stats.is_within_cloud_run_limit());
        
        let summary = stats.format_summary();
        assert!(summary.contains("Peak: 50.00 MB"));
        assert!(summary.contains("Efficiency: 80.00%"));
    }

    #[test]
    fn test_cloud_run_limit_check() {
        let stats = MemoryStats {
            total_allocated: 0,
            total_deallocated: 0,
            current_usage: 0,
            peak_usage: 1024 * 1024 * 1024 * 5, // 5 GB
            duration: std::time::Duration::from_secs(1),
        };
        
        assert!(!stats.is_within_cloud_run_limit());
    }

    #[test]
    fn test_measure_memory() {
        let (result, stats) = measure_memory(|| {
            let mut v = Vec::new();
            for i in 0..1000 {
                v.push(i);
            }
            v.len()
        });
        
        assert_eq!(result, 1000);
        // Note: Actual memory tracking would require global allocator override
        // This test just verifies the API works
    }
}