//! Evidence Collection Framework for Performance Validation
//! Agent 11B - Emergency Performance Validation
//! 
//! Collects and organizes all validation evidence for production readiness assessment

use anyhow::Result;
use chrono::Utc;
use serde::Serialize;
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;

#[derive(Debug, Serialize)]
struct ValidationEvidence {
    validation_type: String,
    agent: String,
    timestamp: String,
    environment: EnvironmentInfo,
    results: EvidenceResults,
    metadata: EvidenceMetadata,
    attestation: ValidationAttestation,
}

#[derive(Debug, Serialize)]
struct EnvironmentInfo {
    platform: String,
    region: String,
    project_id: String,
    service_name: String,
    build_id: Option<String>,
    git_commit: Option<String>,
}

#[derive(Debug, Serialize)]
struct EvidenceResults {
    performance_benchmarks: Vec<PerformanceBenchmark>,
    memory_profiles: Vec<MemoryProfile>,
    concurrent_tests: Vec<ConcurrentTest>,
    infrastructure_tests: Vec<InfrastructureTest>,
    repository_validations: Vec<RepositoryValidation>,
}

#[derive(Debug, Serialize)]
struct EvidenceMetadata {
    cloud_run_config: CloudRunConfig,
    test_environment: TestEnvironment,
    validation_criteria: ValidationCriteria,
    test_repositories: Vec<TestRepository>,
}

#[derive(Debug, Serialize)]
struct CloudRunConfig {
    memory_limit: String,
    cpu_limit: String,
    timeout: String,
    concurrency: u32,
    execution_environment: String,
    max_instances: u32,
    min_instances: u32,
}

#[derive(Debug, Serialize)]
struct TestEnvironment {
    rust_version: String,
    cargo_version: String,
    tree_sitter_version: String,
    docker_version: Option<String>,
    os_info: String,
}

#[derive(Debug, Serialize)]
struct ValidationCriteria {
    target_duration_seconds: u32,
    target_memory_limit_mb: u32,
    minimum_loc: u32,
    success_rate_threshold: f64,
    concurrent_analysis_count: u32,
}

#[derive(Debug, Serialize)]
struct TestRepository {
    name: String,
    url: String,
    expected_loc: usize,
    actual_loc: Option<usize>,
    validation_status: String,
}

#[derive(Debug, Serialize)]
struct PerformanceBenchmark {
    repository: String,
    duration_seconds: u64,
    lines_of_code: usize,
    memory_peak_mb: f64,
    throughput_loc_per_second: f64,
    success: bool,
    meets_target: bool,
}

#[derive(Debug, Serialize)]
struct MemoryProfile {
    test_name: String,
    peak_memory_mb: f64,
    average_memory_mb: f64,
    memory_efficiency: f64,
    within_cloud_run_limit: bool,
}

#[derive(Debug, Serialize)]
struct ConcurrentTest {
    test_id: String,
    concurrent_count: u32,
    total_duration_seconds: u64,
    all_succeeded: bool,
    average_duration_seconds: f64,
}

#[derive(Debug, Serialize)]
struct InfrastructureTest {
    test_name: String,
    component: String,
    result: String,
    details: HashMap<String, String>,
}

#[derive(Debug, Serialize)]
struct RepositoryValidation {
    repository: String,
    total_files: usize,
    total_loc: usize,
    languages: HashMap<String, usize>,
    meets_1m_requirement: bool,
}

#[derive(Debug, Serialize)]
struct ValidationAttestation {
    claim_validated: bool,
    validation_statement: String,
    confidence_level: String,
    evidence_quality: String,
    recommendation: String,
}

async fn collect_validation_evidence() -> Result<ValidationEvidence> {
    let timestamp = Utc::now().to_rfc3339();
    
    println!("📋 Collecting validation evidence...");
    
    // Collect environment information
    let environment = collect_environment_info().await?;
    
    // Collect all test results
    let results = EvidenceResults {
        performance_benchmarks: collect_performance_benchmarks().await?,
        memory_profiles: collect_memory_profiles().await?,
        concurrent_tests: collect_concurrent_tests().await?,
        infrastructure_tests: collect_infrastructure_tests().await?,
        repository_validations: collect_repository_validations().await?,
    };
    
    // Define metadata
    let metadata = EvidenceMetadata {
        cloud_run_config: CloudRunConfig {
            memory_limit: "4Gi".to_string(),
            cpu_limit: "8".to_string(),
            timeout: "300s".to_string(),
            concurrency: 1,
            execution_environment: "gen2".to_string(),
            max_instances: 1,
            min_instances: 0,
        },
        test_environment: collect_test_environment().await?,
        validation_criteria: ValidationCriteria {
            target_duration_seconds: 300,
            target_memory_limit_mb: 4096,
            minimum_loc: 1_000_000,
            success_rate_threshold: 100.0,
            concurrent_analysis_count: 3,
        },
        test_repositories: collect_test_repositories().await?,
    };
    
    // Generate attestation based on results
    let attestation = generate_attestation(&results, &metadata);
    
    Ok(ValidationEvidence {
        validation_type: "Emergency Performance Validation - 1M LOC Claim".to_string(),
        agent: "Agent 11B".to_string(),
        timestamp,
        environment,
        results,
        metadata,
        attestation,
    })
}

async fn collect_environment_info() -> Result<EnvironmentInfo> {
    // Get project ID from gcloud
    let project_id = Command::new("gcloud")
        .args(&["config", "get-value", "project"])
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".to_string());
    
    // Get git commit
    let git_commit = Command::new("git")
        .args(&["rev-parse", "HEAD"])
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .ok();
    
    Ok(EnvironmentInfo {
        platform: "Google Cloud Run".to_string(),
        region: "us-central1".to_string(),
        project_id,
        service_name: "analysis-engine-perf".to_string(),
        build_id: std::env::var("BUILD_ID").ok(),
        git_commit,
    })
}

async fn collect_test_environment() -> Result<TestEnvironment> {
    // Get Rust version
    let rust_version = Command::new("rustc")
        .arg("--version")
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".to_string());
    
    // Get Cargo version
    let cargo_version = Command::new("cargo")
        .arg("--version")
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".to_string());
    
    // Get Docker version
    let docker_version = Command::new("docker")
        .arg("--version")
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .ok();
    
    // Get OS info
    let os_info = std::env::consts::OS.to_string() + " " + std::env::consts::ARCH;
    
    Ok(TestEnvironment {
        rust_version,
        cargo_version,
        tree_sitter_version: "0.22.6".to_string(), // From Cargo.toml
        docker_version,
        os_info,
    })
}

async fn collect_performance_benchmarks() -> Result<Vec<PerformanceBenchmark>> {
    let mut benchmarks = Vec::new();
    let results_dir = Path::new("evidence/agent-11b");
    
    if !results_dir.exists() {
        return Ok(benchmarks);
    }
    
    // Find benchmark result files
    for entry in fs::read_dir(results_dir)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.starts_with("benchmark-") && file_name.ends_with(".json") {
            let content = fs::read_to_string(entry.path())?;
            
            if let Ok(result) = serde_json::from_str::<serde_json::Value>(&content) {
                let benchmark = PerformanceBenchmark {
                    repository: result["repository"].as_str().unwrap_or("unknown").to_string(),
                    duration_seconds: result["duration_seconds"].as_u64().unwrap_or(0),
                    lines_of_code: result["lines_of_code"].as_u64().unwrap_or(0) as usize,
                    memory_peak_mb: result["memory_peak_mb"].as_f64().unwrap_or(0.0),
                    throughput_loc_per_second: calculate_throughput(
                        result["lines_of_code"].as_u64().unwrap_or(0) as usize,
                        result["duration_seconds"].as_u64().unwrap_or(1),
                    ),
                    success: result["success"].as_bool().unwrap_or(false),
                    meets_target: result["meets_5min_target"].as_bool().unwrap_or(false),
                };
                benchmarks.push(benchmark);
            }
        }
    }
    
    // Sort by repository name for consistent ordering
    benchmarks.sort_by(|a, b| a.repository.cmp(&b.repository));
    
    Ok(benchmarks)
}

async fn collect_memory_profiles() -> Result<Vec<MemoryProfile>> {
    let mut profiles = Vec::new();
    
    // Analyze memory usage from performance benchmarks
    let benchmarks = collect_performance_benchmarks().await?;
    
    for benchmark in &benchmarks {
        if benchmark.success {
            let profile = MemoryProfile {
                test_name: format!("{}_analysis", benchmark.repository),
                peak_memory_mb: benchmark.memory_peak_mb,
                average_memory_mb: benchmark.memory_peak_mb * 0.8, // Estimate
                memory_efficiency: calculate_memory_efficiency(
                    benchmark.memory_peak_mb,
                    benchmark.lines_of_code,
                ),
                within_cloud_run_limit: benchmark.memory_peak_mb < 4096.0,
            };
            profiles.push(profile);
        }
    }
    
    Ok(profiles)
}

async fn collect_concurrent_tests() -> Result<Vec<ConcurrentTest>> {
    let mut tests = Vec::new();
    let results_dir = Path::new("evidence/agent-11b");
    
    // Look for concurrent test results
    for i in 1..=3 {
        let file_path = results_dir.join(format!("concurrent-{}-*.json", i));
        
        // Use glob pattern to find files
        if let Ok(paths) = glob::glob(file_path.to_str().unwrap()) {
            for path in paths {
                if let Ok(path) = path {
                    if let Ok(content) = fs::read_to_string(&path) {
                        if let Ok(result) = serde_json::from_str::<serde_json::Value>(&content) {
                            let test = ConcurrentTest {
                                test_id: format!("concurrent-{}", i),
                                concurrent_count: 3,
                                total_duration_seconds: result["duration_seconds"].as_u64().unwrap_or(0),
                                all_succeeded: result["success"].as_bool().unwrap_or(false),
                                average_duration_seconds: result["duration_seconds"].as_f64().unwrap_or(0.0),
                            };
                            tests.push(test);
                        }
                    }
                }
            }
        }
    }
    
    Ok(tests)
}

async fn collect_infrastructure_tests() -> Result<Vec<InfrastructureTest>> {
    let mut tests = Vec::new();
    
    // Cloud Run deployment test
    let mut cloud_run_details = HashMap::new();
    cloud_run_details.insert("service".to_string(), "analysis-engine-perf".to_string());
    cloud_run_details.insert("memory".to_string(), "4Gi".to_string());
    cloud_run_details.insert("cpu".to_string(), "8 vCPU".to_string());
    
    tests.push(InfrastructureTest {
        test_name: "cloud_run_deployment".to_string(),
        component: "Cloud Run".to_string(),
        result: "configured".to_string(),
        details: cloud_run_details,
    });
    
    // Spanner connectivity test
    let mut spanner_details = HashMap::new();
    spanner_details.insert("instance".to_string(), "ccl-spanner-instance".to_string());
    spanner_details.insert("database".to_string(), "ccl-database".to_string());
    
    tests.push(InfrastructureTest {
        test_name: "spanner_connectivity".to_string(),
        component: "Cloud Spanner".to_string(),
        result: "configured".to_string(),
        details: spanner_details,
    });
    
    // Redis connectivity test
    let mut redis_details = HashMap::new();
    redis_details.insert("endpoint".to_string(), "redis://10.0.0.1:6379".to_string());
    
    tests.push(InfrastructureTest {
        test_name: "redis_connectivity".to_string(),
        component: "Redis".to_string(),
        result: "configured".to_string(),
        details: redis_details,
    });
    
    Ok(tests)
}

async fn collect_repository_validations() -> Result<Vec<RepositoryValidation>> {
    let mut validations = Vec::new();
    let repo_summary_path = Path::new("test-data/large-repositories/repository-summary.json");
    
    if repo_summary_path.exists() {
        let content = fs::read_to_string(repo_summary_path)?;
        if let Ok(summary) = serde_json::from_str::<serde_json::Value>(&content) {
            if let Some(repos) = summary["repositories"].as_array() {
                for repo in repos {
                    let validation = RepositoryValidation {
                        repository: repo["name"].as_str().unwrap_or("unknown").to_string(),
                        total_files: 0, // Would need to count from filesystem
                        total_loc: repo["loc_count"].as_u64().unwrap_or(0) as usize,
                        languages: HashMap::new(), // Would need detailed analysis
                        meets_1m_requirement: repo["meets_1m_requirement"].as_bool().unwrap_or(false),
                    };
                    validations.push(validation);
                }
            }
        }
    }
    
    Ok(validations)
}

async fn collect_test_repositories() -> Result<Vec<TestRepository>> {
    let mut repositories = vec![
        TestRepository {
            name: "kubernetes".to_string(),
            url: "https://github.com/kubernetes/kubernetes.git".to_string(),
            expected_loc: 1_500_000,
            actual_loc: None,
            validation_status: "pending".to_string(),
        },
        TestRepository {
            name: "rust".to_string(),
            url: "https://github.com/rust-lang/rust.git".to_string(),
            expected_loc: 2_500_000,
            actual_loc: None,
            validation_status: "pending".to_string(),
        },
        TestRepository {
            name: "tensorflow".to_string(),
            url: "https://github.com/tensorflow/tensorflow.git".to_string(),
            expected_loc: 3_500_000,
            actual_loc: None,
            validation_status: "pending".to_string(),
        },
        TestRepository {
            name: "linux".to_string(),
            url: "https://github.com/torvalds/linux.git".to_string(),
            expected_loc: 20_000_000,
            actual_loc: None,
            validation_status: "pending".to_string(),
        },
    ];
    
    // Update with actual LOC if available
    let validations = collect_repository_validations().await?;
    for repo in &mut repositories {
        if let Some(validation) = validations.iter().find(|v| v.repository == repo.name) {
            repo.actual_loc = Some(validation.total_loc);
            repo.validation_status = if validation.meets_1m_requirement {
                "validated".to_string()
            } else {
                "below_threshold".to_string()
            };
        }
    }
    
    Ok(repositories)
}

fn calculate_throughput(loc: usize, duration_seconds: u64) -> f64 {
    if duration_seconds > 0 {
        loc as f64 / duration_seconds as f64
    } else {
        0.0
    }
}

fn calculate_memory_efficiency(memory_mb: f64, loc: usize) -> f64 {
    if loc > 0 {
        // Return bytes per line of code
        (memory_mb * 1024.0 * 1024.0) / loc as f64
    } else {
        0.0
    }
}

fn generate_attestation(results: &EvidenceResults, _metadata: &EvidenceMetadata) -> ValidationAttestation {
    // Analyze performance benchmarks
    let total_benchmarks = results.performance_benchmarks.len();
    let successful_benchmarks = results.performance_benchmarks.iter()
        .filter(|b| b.success && b.meets_target)
        .count();
    
    let million_loc_benchmarks = results.performance_benchmarks.iter()
        .filter(|b| b.lines_of_code >= 1_000_000)
        .collect::<Vec<_>>();
    
    let million_loc_validated = !million_loc_benchmarks.is_empty() &&
        million_loc_benchmarks.iter().all(|b| b.success && b.meets_target);
    
    // Analyze memory profiles
    let memory_within_limits = results.memory_profiles.iter()
        .all(|p| p.within_cloud_run_limit);
    
    // Analyze concurrent tests
    let concurrent_success = results.concurrent_tests.iter()
        .all(|t| t.all_succeeded);
    
    // Generate validation statement
    let validation_statement = if million_loc_validated {
        format!(
            "The analysis-engine successfully processed {} repositories with 1M+ LOC in under 5 minutes. \
            All {} performance benchmarks passed with 100% success rate.",
            million_loc_benchmarks.len(),
            successful_benchmarks
        )
    } else {
        format!(
            "The analysis-engine failed to validate the 1M LOC in <5 minutes claim. \
            Only {} out of {} benchmarks passed. Maximum validated capacity: {} LOC.",
            successful_benchmarks,
            total_benchmarks,
            results.performance_benchmarks.iter()
                .filter(|b| b.success && b.meets_target)
                .map(|b| b.lines_of_code)
                .max()
                .unwrap_or(0)
        )
    };
    
    // Determine confidence level
    let confidence_level = if total_benchmarks >= 4 && million_loc_validated {
        "HIGH".to_string()
    } else if total_benchmarks >= 2 {
        "MEDIUM".to_string()
    } else {
        "LOW".to_string()
    };
    
    // Determine evidence quality
    let evidence_quality = if results.repository_validations.len() >= 4 &&
        results.performance_benchmarks.len() >= 4 &&
        !results.concurrent_tests.is_empty() {
        "COMPREHENSIVE".to_string()
    } else if results.performance_benchmarks.len() >= 2 {
        "ADEQUATE".to_string()
    } else {
        "LIMITED".to_string()
    };
    
    // Generate recommendation
    let recommendation = if million_loc_validated && memory_within_limits && concurrent_success {
        "PROCEED WITH PRODUCTION DEPLOYMENT - All validation criteria met".to_string()
    } else if million_loc_validated && memory_within_limits {
        "PROCEED WITH CAUTION - Performance validated but concurrent analysis needs improvement".to_string()
    } else {
        "HALT PRODUCTION DEPLOYMENT - Critical performance criteria not met".to_string()
    };
    
    ValidationAttestation {
        claim_validated: million_loc_validated,
        validation_statement,
        confidence_level,
        evidence_quality,
        recommendation,
    }
}

fn save_evidence_report(evidence: &ValidationEvidence) -> Result<PathBuf> {
    let evidence_dir = Path::new("evidence/agent-11b");
    fs::create_dir_all(evidence_dir)?;
    
    // Generate filenames with timestamp
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S").to_string();
    let json_path = evidence_dir.join(format!("validation-evidence-{}.json", timestamp));
    let summary_path = evidence_dir.join(format!("validation-evidence-summary-{}.md", timestamp));
    
    // Save JSON evidence
    let json_content = serde_json::to_string_pretty(evidence)?;
    fs::write(&json_path, json_content)?;
    
    // Generate and save markdown summary
    let markdown_summary = generate_markdown_report(evidence);
    fs::write(&summary_path, markdown_summary)?;
    
    Ok(json_path)
}

fn generate_markdown_report(evidence: &ValidationEvidence) -> String {
    format!(r#"# Validation Evidence Report

**Agent**: {}  
**Type**: {}  
**Timestamp**: {}  
**Environment**: {} ({})  

## Attestation

**Claim Validated**: **{}**  
**Confidence Level**: {}  
**Evidence Quality**: {}  

### Validation Statement
{}

### Recommendation
**{}**

## Performance Results

### Benchmarks
| Repository | Duration | LOC | Memory | Throughput | Result |
|------------|----------|-----|--------|------------|---------|
{}

### Memory Profiles
- Peak Memory Usage: {:.1} MB (max)
- Memory Efficiency: {:.1} bytes/LOC (average)
- Within Cloud Run Limits: {}

### Concurrent Analysis
- Tests Performed: {}
- All Succeeded: {}
- Average Duration: {:.1}s

## Test Environment
- Rust Version: {}
- Cargo Version: {}
- Tree-sitter Version: {}
- Platform: {}

## Validation Criteria
- Target Duration: {} seconds
- Memory Limit: {} MB
- Minimum LOC: {}
- Success Threshold: {}%

## Evidence Location
- JSON Evidence: `evidence/agent-11b/validation-evidence-*.json`
- Benchmark Results: `evidence/agent-11b/benchmark-*.json`
- Performance Reports: `evidence/agent-11b/performance-validation-*.md`

---
Generated by Agent 11B - Emergency Performance Validation
"#,
        evidence.agent,
        evidence.validation_type,
        evidence.timestamp,
        evidence.environment.platform,
        evidence.environment.region,
        if evidence.attestation.claim_validated { "YES ✅" } else { "NO ❌" },
        evidence.attestation.confidence_level,
        evidence.attestation.evidence_quality,
        evidence.attestation.validation_statement,
        evidence.attestation.recommendation,
        evidence.results.performance_benchmarks.iter()
            .map(|b| format!("| {} | {}s | {} | {:.1} MB | {:.0} LOC/s | {} |",
                b.repository,
                b.duration_seconds,
                b.lines_of_code,
                b.memory_peak_mb,
                b.throughput_loc_per_second,
                if b.success && b.meets_target { "✅" } else { "❌" }
            ))
            .collect::<Vec<_>>()
            .join("\n"),
        evidence.results.memory_profiles.iter()
            .map(|p| p.peak_memory_mb)
            .fold(0.0, f64::max),
        evidence.results.memory_profiles.iter()
            .map(|p| p.memory_efficiency)
            .sum::<f64>() / evidence.results.memory_profiles.len().max(1) as f64,
        evidence.results.memory_profiles.iter()
            .all(|p| p.within_cloud_run_limit),
        evidence.results.concurrent_tests.len(),
        evidence.results.concurrent_tests.iter()
            .all(|t| t.all_succeeded),
        evidence.results.concurrent_tests.iter()
            .map(|t| t.average_duration_seconds)
            .sum::<f64>() / evidence.results.concurrent_tests.len().max(1) as f64,
        evidence.metadata.test_environment.rust_version,
        evidence.metadata.test_environment.cargo_version,
        evidence.metadata.test_environment.tree_sitter_version,
        evidence.metadata.test_environment.os_info,
        evidence.metadata.validation_criteria.target_duration_seconds,
        evidence.metadata.validation_criteria.target_memory_limit_mb,
        evidence.metadata.validation_criteria.minimum_loc,
        evidence.metadata.validation_criteria.success_rate_threshold,
    )
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("📦 Evidence Collection Framework");
    println!("================================");
    println!("Agent 11B - Emergency Performance Validation");
    println!();
    
    // Collect all evidence
    let evidence = collect_validation_evidence().await?;
    
    // Save evidence report
    let evidence_path = save_evidence_report(&evidence)?;
    
    // Print summary
    println!("\n✅ Evidence Collection Complete!");
    println!("================================");
    println!("Claim Validated: {}", 
        if evidence.attestation.claim_validated { "YES ✅" } else { "NO ❌" });
    println!("Confidence Level: {}", evidence.attestation.confidence_level);
    println!("Evidence Quality: {}", evidence.attestation.evidence_quality);
    println!("\nRecommendation:");
    println!("{}", evidence.attestation.recommendation);
    println!("\nEvidence saved to: {:?}", evidence_path);
    
    // Exit with appropriate code
    if evidence.attestation.claim_validated {
        std::process::exit(0);
    } else {
        std::process::exit(1);
    }
}