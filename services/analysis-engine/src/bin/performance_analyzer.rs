//! Performance Analysis Tool for Emergency Validation
//! Agent 11B - Emergency Performance Validation
//! 
//! This tool analyzes performance test results and generates comprehensive reports

use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, Deserialize, Serialize)]
struct BenchmarkResult {
    repository: String,
    repository_url: String,
    #[serde(default)]
    analysis_id: String,
    duration_seconds: u64,
    #[serde(default)]
    processing_time_ms: u64,
    lines_of_code: usize,
    #[serde(default)]
    files_analyzed: usize,
    memory_peak_mb: f64,
    success: bool,
    #[serde(default)]
    meets_5min_target: bool,
    #[serde(default)]
    error_message: Option<String>,
    #[serde(default)]
    http_code: String,
    timestamp: String,
}

#[derive(Debug, Serialize)]
struct PerformanceReport {
    validation_timestamp: String,
    total_tests: usize,
    passed_tests: usize,
    failed_tests: usize,
    results: Vec<BenchmarkResult>,
    summary: PerformanceSummary,
    recommendations: Vec<String>,
}

#[derive(Debug, Serialize)]
struct PerformanceSummary {
    can_meet_1m_loc_claim: bool,
    average_duration_seconds: f64,
    max_memory_usage_mb: f64,
    total_lines_analyzed: usize,
    average_throughput_loc_per_second: f64,
    success_rate_percent: f64,
    recommended_claim: String,
    optimization_opportunities: Vec<String>,
}

#[derive(Debug, Serialize)]
struct RepositoryAnalysis {
    name: String,
    loc: usize,
    duration_seconds: u64,
    throughput_loc_per_second: f64,
    memory_mb: f64,
    memory_per_loc: f64,
    meets_target: bool,
}

async fn analyze_performance_results(results_dir: &Path) -> Result<PerformanceReport> {
    let mut results = Vec::new();
    
    // Find the latest timestamp directory
    let latest_timestamp = find_latest_timestamp(results_dir)?;
    println!("Analyzing results from timestamp: {}", latest_timestamp);
    
    // Load all benchmark results
    for entry in fs::read_dir(results_dir)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.starts_with("benchmark-") && file_name.contains(&latest_timestamp) {
            let content = fs::read_to_string(entry.path())
                .context(format!("Failed to read {}", file_name))?;
            
            match serde_json::from_str::<BenchmarkResult>(&content) {
                Ok(result) => results.push(result),
                Err(e) => eprintln!("Warning: Failed to parse {}: {}", file_name, e),
            }
        }
    }
    
    if results.is_empty() {
        return Err(anyhow::anyhow!("No benchmark results found for timestamp {}", latest_timestamp));
    }
    
    // Calculate summary statistics
    let total_tests = results.len();
    let passed_tests = results.iter()
        .filter(|r| r.success && r.meets_5min_target)
        .count();
    let failed_tests = total_tests - passed_tests;
    
    // Calculate performance metrics
    let successful_results: Vec<_> = results.iter()
        .filter(|r| r.success)
        .collect();
    
    let average_duration = if !successful_results.is_empty() {
        successful_results.iter()
            .map(|r| r.duration_seconds as f64)
            .sum::<f64>() / successful_results.len() as f64
    } else {
        0.0
    };
    
    let max_memory = results.iter()
        .map(|r| r.memory_peak_mb)
        .fold(0.0, f64::max);
    
    let total_lines_analyzed = successful_results.iter()
        .map(|r| r.lines_of_code)
        .sum();
    
    let average_throughput = if average_duration > 0.0 {
        total_lines_analyzed as f64 / (successful_results.len() as f64 * average_duration)
    } else {
        0.0
    };
    
    // Determine if we can meet the 1M LOC claim
    let million_loc_results: Vec<_> = results.iter()
        .filter(|r| r.lines_of_code >= 1_000_000)
        .collect();
    
    let can_meet_claim = !million_loc_results.is_empty() && 
        million_loc_results.iter().all(|r| r.success && r.duration_seconds <= 300);
    
    // Generate recommended claim based on actual performance
    let recommended_claim = generate_recommended_claim(&results);
    
    // Identify optimization opportunities
    let optimization_opportunities = identify_optimization_opportunities(&results);
    
    // Generate recommendations
    let recommendations = generate_recommendations(&results, can_meet_claim);
    
    let summary = PerformanceSummary {
        can_meet_1m_loc_claim: can_meet_claim,
        average_duration_seconds: average_duration,
        max_memory_usage_mb: max_memory,
        total_lines_analyzed,
        average_throughput_loc_per_second: average_throughput,
        success_rate_percent: (successful_results.len() as f64 / total_tests as f64) * 100.0,
        recommended_claim,
        optimization_opportunities,
    };
    
    Ok(PerformanceReport {
        validation_timestamp: latest_timestamp,
        total_tests,
        passed_tests,
        failed_tests,
        results,
        summary,
        recommendations,
    })
}

fn find_latest_timestamp(results_dir: &Path) -> Result<String> {
    let mut timestamps = Vec::new();
    
    for entry in fs::read_dir(results_dir)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.starts_with("benchmark-") {
            // Extract timestamp from filename (format: benchmark-repo-YYYYMMDD_HHMMSS.json)
            if let Some(timestamp) = extract_timestamp(&file_name) {
                timestamps.push(timestamp);
            }
        }
    }
    
    timestamps.sort();
    timestamps.last()
        .cloned()
        .ok_or_else(|| anyhow::anyhow!("No benchmark results found"))
}

fn extract_timestamp(filename: &str) -> Option<String> {
    // Format: benchmark-repo-YYYYMMDD_HHMMSS.json
    let parts: Vec<&str> = filename.split('-').collect();
    if parts.len() >= 3 {
        let timestamp_part = parts.last()?.trim_end_matches(".json");
        Some(timestamp_part.to_string())
    } else {
        None
    }
}

fn generate_recommended_claim(results: &[BenchmarkResult]) -> String {
    let successful_results: Vec<_> = results.iter()
        .filter(|r| r.success && r.duration_seconds <= 300)
        .collect();
    
    if successful_results.is_empty() {
        return "Performance validation required - no successful results".to_string();
    }
    
    // Find the largest repository successfully analyzed under 5 minutes
    let max_validated_loc = successful_results.iter()
        .map(|r| r.lines_of_code)
        .max()
        .unwrap_or(0);
    
    if max_validated_loc >= 1_000_000 {
        let max_in_millions = max_validated_loc as f64 / 1_000_000.0;
        format!("{:.1}M LOC in <5 minutes (validated)", max_in_millions)
    } else if max_validated_loc >= 500_000 {
        format!("{}K LOC in <5 minutes (validated)", max_validated_loc / 1000)
    } else {
        "Performance capabilities require further optimization".to_string()
    }
}

fn identify_optimization_opportunities(results: &[BenchmarkResult]) -> Vec<String> {
    let mut opportunities = Vec::new();
    
    // Check memory usage patterns
    let high_memory_results: Vec<_> = results.iter()
        .filter(|r| r.memory_peak_mb > 3000.0)
        .collect();
    
    if !high_memory_results.is_empty() {
        opportunities.push(format!(
            "High memory usage detected ({} tests exceeded 3GB) - optimize AST memory management",
            high_memory_results.len()
        ));
    }
    
    // Check for slow analyses
    let slow_analyses: Vec<_> = results.iter()
        .filter(|r| r.success && r.duration_seconds > 240)
        .collect();
    
    if !slow_analyses.is_empty() {
        opportunities.push(format!(
            "Performance bottlenecks detected ({} tests took >4 minutes) - investigate parser performance",
            slow_analyses.len()
        ));
    }
    
    // Check failure patterns
    let failures: Vec<_> = results.iter()
        .filter(|r| !r.success)
        .collect();
    
    if !failures.is_empty() {
        opportunities.push(format!(
            "Reliability issues detected ({} failures) - improve error handling and recovery",
            failures.len()
        ));
    }
    
    // Check memory efficiency
    let memory_per_loc: Vec<f64> = results.iter()
        .filter(|r| r.success && r.lines_of_code > 0)
        .map(|r| r.memory_peak_mb * 1024.0 * 1024.0 / r.lines_of_code as f64)
        .collect();
    
    if !memory_per_loc.is_empty() {
        let avg_bytes_per_loc = memory_per_loc.iter().sum::<f64>() / memory_per_loc.len() as f64;
        if avg_bytes_per_loc > 100.0 {
            opportunities.push(format!(
                "High memory per LOC ({:.0} bytes/line) - optimize data structures",
                avg_bytes_per_loc
            ));
        }
    }
    
    // Check throughput variance
    let throughputs: Vec<f64> = results.iter()
        .filter(|r| r.success && r.duration_seconds > 0)
        .map(|r| r.lines_of_code as f64 / r.duration_seconds as f64)
        .collect();
    
    if throughputs.len() > 1 {
        let min_throughput = throughputs.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_throughput = throughputs.iter().fold(0.0_f64, |a, &b| a.max(b));
        
        if max_throughput > min_throughput * 2.0 {
            opportunities.push(
                "High performance variance detected - investigate language-specific bottlenecks".to_string()
            );
        }
    }
    
    opportunities
}

fn generate_recommendations(results: &[BenchmarkResult], can_meet_claim: bool) -> Vec<String> {
    let mut recommendations = Vec::new();
    
    if can_meet_claim {
        recommendations.push("✅ The 1M LOC in <5 minutes claim is VALIDATED".to_string());
        recommendations.push("Proceed with production deployment with confidence".to_string());
    } else {
        recommendations.push("❌ The 1M LOC in <5 minutes claim is NOT validated".to_string());
        recommendations.push("CRITICAL: Update customer communications immediately".to_string());
    }
    
    // Memory recommendations
    let max_memory = results.iter().map(|r| r.memory_peak_mb).fold(0.0, f64::max);
    if max_memory > 3500.0 {
        recommendations.push(format!(
            "⚠️ Memory usage ({:.0}MB) approaching Cloud Run limit - implement memory optimization",
            max_memory
        ));
    }
    
    // Performance recommendations
    let slow_count = results.iter().filter(|r| r.duration_seconds > 300).count();
    if slow_count > 0 {
        recommendations.push(format!(
            "🔧 {} repositories exceeded 5-minute target - optimize parsing for large files",
            slow_count
        ));
    }
    
    // Reliability recommendations
    let failure_rate = results.iter().filter(|r| !r.success).count() as f64 / results.len() as f64;
    if failure_rate > 0.1 {
        recommendations.push(format!(
            "⚠️ High failure rate ({:.0}%) - improve error handling and retry logic",
            failure_rate * 100.0
        ));
    }
    
    recommendations
}

fn generate_markdown_summary(report: &PerformanceReport) -> String {
    let mut md = String::new();
    
    md.push_str(&format!(r#"# Performance Validation Summary

**Generated**: {}  
**Agent**: 11B - Emergency Performance Validation  
**Status**: {}

## Executive Summary

- **Total Tests**: {}
- **Passed**: {} ({:.1}%)
- **Failed**: {} ({:.1}%)
- **Can Meet 1M LOC Claim**: **{}**
- **Recommended Claim**: {}

## Test Results

| Repository | Duration (s) | LOC | Memory (MB) | Throughput (LOC/s) | Status |
|------------|--------------|-----|-------------|-------------------|---------|
"#,
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
        if report.summary.can_meet_1m_loc_claim { "PASSED ✅" } else { "FAILED ❌" },
        report.total_tests,
        report.passed_tests,
        (report.passed_tests as f64 / report.total_tests as f64) * 100.0,
        report.failed_tests,
        (report.failed_tests as f64 / report.total_tests as f64) * 100.0,
        if report.summary.can_meet_1m_loc_claim { "YES" } else { "NO" },
        report.summary.recommended_claim,
    ));
    
    // Add test results table
    for result in &report.results {
        let throughput = if result.duration_seconds > 0 {
            result.lines_of_code as f64 / result.duration_seconds as f64
        } else {
            0.0
        };
        
        md.push_str(&format!(
            "| {} | {} | {} | {:.1} | {:.0} | {} |\n",
            result.repository,
            result.duration_seconds,
            result.lines_of_code,
            result.memory_peak_mb,
            throughput,
            if result.success && result.meets_5min_target { "✅" } else { "❌" }
        ));
    }
    
    md.push_str(&format!(r#"

## Performance Metrics

- **Average Duration**: {:.1} seconds
- **Max Memory Usage**: {:.1} MB
- **Average Throughput**: {:.0} LOC/second
- **Success Rate**: {:.1}%

## Key Findings

### Performance Characteristics
"#,
        report.summary.average_duration_seconds,
        report.summary.max_memory_usage_mb,
        report.summary.average_throughput_loc_per_second,
        report.summary.success_rate_percent,
    ));
    
    // Add repository-specific analysis
    let mut repo_analyses = Vec::new();
    for result in &report.results {
        if result.success {
            repo_analyses.push(RepositoryAnalysis {
                name: result.repository.clone(),
                loc: result.lines_of_code,
                duration_seconds: result.duration_seconds,
                throughput_loc_per_second: result.lines_of_code as f64 / result.duration_seconds as f64,
                memory_mb: result.memory_peak_mb,
                memory_per_loc: result.memory_peak_mb * 1024.0 * 1024.0 / result.lines_of_code as f64,
                meets_target: result.meets_5min_target,
            });
        }
    }
    
    // Sort by LOC descending
    repo_analyses.sort_by(|a, b| b.loc.cmp(&a.loc));
    
    for analysis in &repo_analyses {
        md.push_str(&format!(
            "- **{}**: {} LOC in {}s ({:.0} LOC/s, {:.1} MB, {:.1} bytes/LOC)\n",
            analysis.name,
            format_number(analysis.loc),
            analysis.duration_seconds,
            analysis.throughput_loc_per_second,
            analysis.memory_mb,
            analysis.memory_per_loc,
        ));
    }
    
    // Add optimization opportunities
    if !report.summary.optimization_opportunities.is_empty() {
        md.push_str("\n## Optimization Opportunities\n\n");
        for opportunity in &report.summary.optimization_opportunities {
            md.push_str(&format!("- {}\n", opportunity));
        }
    }
    
    // Add recommendations
    md.push_str("\n## Recommendations\n\n");
    for recommendation in &report.recommendations {
        md.push_str(&format!("- {}\n", recommendation));
    }
    
    // Add conclusion
    md.push_str(&format!(r#"

## Conclusion

{}

### Next Steps

"#,
        if report.summary.can_meet_1m_loc_claim {
            "The analysis-engine successfully validates its core performance claim of analyzing 1M lines of code in under 5 minutes. The service is ready for production deployment with the validated performance characteristics."
        } else {
            "The analysis-engine does NOT meet the claimed performance target of 1M LOC in <5 minutes. Immediate action is required to either optimize performance or adjust customer communications to reflect actual capabilities."
        }
    ));
    
    if report.summary.can_meet_1m_loc_claim {
        md.push_str(r#"1. ✅ Proceed with production deployment
2. ✅ Update documentation with validated performance metrics
3. ✅ Configure production monitoring based on observed characteristics
4. ✅ Set up performance regression testing with these baselines
"#);
    } else {
        md.push_str(r#"1. ❌ HALT production deployment
2. ❌ Update customer communications with accurate performance claims
3. ❌ Implement performance optimizations identified above
4. ❌ Re-run validation after optimizations
"#);
    }
    
    md.push_str(&format!(r#"

---

**Validation Evidence Location**: `evidence/agent-11b/`  
**Test Timestamp**: {}  
**Confidence Level**: HIGH (based on actual performance measurements)
"#, report.validation_timestamp));
    
    md
}

fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    let mut count = 0;
    
    for c in s.chars().rev() {
        if count > 0 && count % 3 == 0 {
            result.push(',');
        }
        result.push(c);
        count += 1;
    }
    
    result.chars().rev().collect()
}

#[tokio::main]
async fn main() -> Result<()> {
    let results_dir = Path::new("evidence/agent-11b");
    
    if !results_dir.exists() {
        eprintln!("Error: Results directory not found at {:?}", results_dir);
        eprintln!("Run the performance validation tests first.");
        std::process::exit(1);
    }
    
    println!("🔍 Analyzing Performance Validation Results...");
    println!("============================================");
    
    let report = analyze_performance_results(results_dir).await?;
    
    // Save JSON report
    let report_json = serde_json::to_string_pretty(&report)?;
    let json_path = results_dir.join(format!("performance-validation-report-{}.json", report.validation_timestamp));
    fs::write(&json_path, &report_json)?;
    println!("✅ JSON report saved to: {:?}", json_path);
    
    // Generate and save markdown summary
    let summary_md = generate_markdown_summary(&report);
    let md_path = results_dir.join(format!("performance-validation-summary-{}.md", report.validation_timestamp));
    fs::write(&md_path, &summary_md)?;
    println!("✅ Markdown summary saved to: {:?}", md_path);
    
    // Print key results to console
    println!("\n📊 Performance Validation Results:");
    println!("==================================");
    println!("Total Tests: {}", report.total_tests);
    println!("Passed: {} ({:.1}%)", report.passed_tests, 
        (report.passed_tests as f64 / report.total_tests as f64) * 100.0);
    println!("Failed: {} ({:.1}%)", report.failed_tests,
        (report.failed_tests as f64 / report.total_tests as f64) * 100.0);
    
    println!("\n🎯 1M LOC Claim Validation:");
    println!("===========================");
    println!("Can Meet Claim: {}", 
        if report.summary.can_meet_1m_loc_claim { "✅ YES" } else { "❌ NO" });
    println!("Recommended Claim: {}", report.summary.recommended_claim);
    
    if !report.summary.optimization_opportunities.is_empty() {
        println!("\n🔧 Optimization Opportunities:");
        println!("=============================");
        for (i, opp) in report.summary.optimization_opportunities.iter().enumerate() {
            println!("{}. {}", i + 1, opp);
        }
    }
    
    println!("\n💡 Recommendations:");
    println!("==================");
    for recommendation in &report.recommendations {
        println!("• {}", recommendation);
    }
    
    println!("\n✅ Analysis complete!");
    
    Ok(())
}