#!/bin/bash
set -e

echo "=== VALIDATION CHECKPOINT $(date) ==="

# Security validation (critical path)
echo "[SECURITY] Zero tolerance clippy check..."
cargo clippy -- -D warnings || { echo "FAIL: Clippy warnings detected"; exit 1; }

# Build validation
echo "[BUILD] Release build validation..."
cargo build --release || { echo "FAIL: Build failed"; exit 1; }

# Test validation
echo "[TEST] Comprehensive test validation..."
cargo test --workspace || { echo "FAIL: Tests failed"; exit 1; }

# Performance validation
echo "[PERFORMANCE] Benchmark validation..."
cargo bench --workspace > /tmp/bench-current.txt || { echo "FAIL: Benchmarks failed"; exit 1; }

# Structure-specific validation
echo "[STRUCTURE] Specific structure validation..."
cargo clippy -- -D clippy::field_reassign_with_default || { echo "FAIL: Field reassignment warnings"; exit 1; }
cargo clippy -- -D clippy::too_many_arguments || { echo "FAIL: Too many arguments warnings"; exit 1; }
cargo clippy -- -D clippy::cognitive_complexity || { echo "FAIL: Cognitive complexity warnings"; exit 1; }

# Format validation
echo "[FORMAT] Code formatting validation..."
cargo fmt -- --check || { echo "FAIL: Code formatting issues"; exit 1; }

# Security validation
echo "[SECURITY] Unsafe block documentation validation..."
UNSAFE_COUNT=$(rg -n "unsafe" src/ | wc -l)
SAFETY_COUNT=$(rg -B2 -A2 "unsafe" src/ | grep -c "// SAFETY:" || echo 0)
if [ "$UNSAFE_COUNT" -ne "$SAFETY_COUNT" ]; then
    echo "FAIL: $UNSAFE_COUNT unsafe blocks, only $SAFETY_COUNT documented with SAFETY comments"
    exit 1
fi

echo "✅ ALL VALIDATIONS PASSED"