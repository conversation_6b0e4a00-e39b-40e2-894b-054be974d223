# Cloud Build configuration for performance testing deployment
# Agent 11B - Emergency Performance Validation
# This deploys analysis-engine with production specifications for 1M LOC validation

substitutions:
  _SERVICE_NAME: 'analysis-engine-perf'
  _REGION: 'us-central1'
  _IMAGE_NAME: 'analysis-engine-perf'

steps:
  # Step 1: Build optimized Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:latest'
      - '--build-arg'
      - 'RUSTFLAGS=-C target-cpu=native -C opt-level=3'
      - '-f'
      - 'Dockerfile'
      - '.'
    env:
      - 'DOCKER_BUILDKIT=1'
    timeout: '1200s' # 20 minutes for Rust compilation

  # Step 2: Push image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '--all-tags'
      - 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}'

  # Step 3: Deploy to Cloud Run with production specifications
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image=gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$BUILD_ID'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--memory=4Gi'          # Maximum memory for 1M LOC processing
      - '--cpu=8'               # Maximum CPU allocation
      - '--cpu-boost'           # Enable CPU boost for faster cold starts
      - '--concurrency=1'      # Single request at a time for accurate benchmarking
      - '--max-instances=1'     # Single instance to measure individual performance
      - '--min-instances=0'     # Allow scale to zero between tests
      - '--timeout=3600'        # 60 minutes timeout for large repository analysis
      - '--no-traffic'          # Don't route production traffic
      - '--tag=perf-test'       # Tag for performance testing
      - '--execution-environment=gen2' # Use 2nd gen for better performance
      - '--service-account=${_SERVICE_NAME}@$PROJECT_ID.iam.gserviceaccount.com'
      - '--set-env-vars=RUST_LOG=info,RUST_BACKTRACE=1'
      - '--set-env-vars=PERFORMANCE_TEST_MODE=true'
      - '--set-env-vars=MEMORY_TRACKING_ENABLED=true'
      - '--set-env-vars=SPANNER_INSTANCE_ID=ccl-spanner-instance'
      - '--set-env-vars=SPANNER_DATABASE_ID=ccl-database'
      - '--set-env-vars=REDIS_URL=redis://10.0.0.1:6379' # Update with actual Redis endpoint
      - '--set-secrets=JWT_SECRET=jwt-secret:latest'
      - '--set-secrets=GITHUB_TOKEN=github-token:latest'
      - '--vpc-connector=projects/$PROJECT_ID/locations/${_REGION}/connectors/ccl-vpc-connector'
      - '--vpc-egress=private-ranges-only'
    timeout: '600s'

  # Step 4: Configure traffic (keep at 0% for performance testing)
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--to-tags=perf-test=0' # No production traffic
    
  # Step 5: Get service URL for testing
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        SERVICE_URL=$(gcloud run services describe ${_SERVICE_NAME} \
          --region=${_REGION} \
          --platform=managed \
          --format='value(status.address.url)')
        
        TAGGED_URL=$(gcloud run services describe ${_SERVICE_NAME} \
          --region=${_REGION} \
          --platform=managed \
          --format='value(status.traffic[].url)' | grep perf-test)
        
        echo "Performance test service deployed!"
        echo "Service URL: $${SERVICE_URL}"
        echo "Tagged URL: $${TAGGED_URL}"
        echo ""
        echo "To run performance tests, use:"
        echo "  ./scripts/performance-validation/execute-performance-validation.sh $${TAGGED_URL}"

  # Step 6: Warm up the service
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        TAGGED_URL=$(gcloud run services describe ${_SERVICE_NAME} \
          --region=${_REGION} \
          --platform=managed \
          --format='value(status.traffic[].url)' | grep perf-test)
        
        echo "Warming up service..."
        curl -s -X GET "$${TAGGED_URL}/health" \
          -H "Authorization: Bearer $(gcloud auth print-identity-token)" || true
        
        sleep 5
        
        curl -s -X GET "$${TAGGED_URL}/api/v1/languages" \
          -H "Authorization: Bearer $(gcloud auth print-identity-token)" || true
        
        echo "Service warmed up and ready for performance testing!"

# Build configuration
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8' # Use high-CPU machine for faster builds
  substitution_option: 'ALLOW_LOOSE'

# Timeout for entire build
timeout: '3600s' # 1 hour total

# Store build logs
artifacts:
  objects:
    location: 'gs://$PROJECT_ID-build-artifacts/performance-tests/$BUILD_ID'
    paths:
      - '/workspace/build.log'