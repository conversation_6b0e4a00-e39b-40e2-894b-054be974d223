#!/bin/bash
# Collect Large Test Repositories for 1M LOC Performance Validation
# Agent 11B - Emergency Performance Validation

set -e

REPO_DIR="test-data/large-repositories"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../" && pwd)"

echo "🚀 Starting Test Repository Collection"
echo "===================================="
echo "Project Root: $PROJECT_ROOT"
echo "Repository Directory: $PROJECT_ROOT/$REPO_DIR"

# Create repository directory
mkdir -p "$PROJECT_ROOT/$REPO_DIR"
cd "$PROJECT_ROOT"

# Test repositories from feature request
declare -A TEST_REPOS=(
    ["rust"]="https://github.com/rust-lang/rust.git"
    ["linux"]="https://github.com/torvalds/linux.git"
    ["tensorflow"]="https://github.com/tensorflow/tensorflow.git"
    ["kubernetes"]="https://github.com/kubernetes/kubernetes.git"
    ["chromium"]="https://github.com/chromium/chromium.git"
)

# Function to count LOC for supported languages
count_loc() {
    local repo_path=$1
    local total_loc=0
    
    # Supported extensions based on analysis-engine capabilities
    local extensions=(
        "rs" "py" "go" "js" "ts" "tsx" "jsx" "java" "cpp" "c" "cc" "cxx" "h" "hpp"
        "rb" "php" "cs" "swift" "kt" "m" "mm" "scala" "r" "jl" "lua" "pl" "sh"
    )
    
    # Build find command with all extensions
    local find_cmd="find \"$repo_path\" -type f \("
    for i in "${!extensions[@]}"; do
        if [ $i -gt 0 ]; then
            find_cmd+=" -o"
        fi
        find_cmd+=" -name \"*.${extensions[$i]}\""
    done
    find_cmd+=" \) -print0"
    
    # Count lines in all matching files
    eval "$find_cmd" | while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local lines=$(wc -l < "$file" 2>/dev/null || echo 0)
            total_loc=$((total_loc + lines))
        fi
    done
    
    echo $total_loc
}

# Clone or update repositories
for name in "${!TEST_REPOS[@]}"; do
    echo ""
    echo "📦 Processing $name repository..."
    echo "--------------------------------"
    
    repo_path="$REPO_DIR/$name"
    
    if [ -d "$repo_path" ]; then
        echo "Repository already exists, updating..."
        cd "$repo_path"
        git fetch --depth=1 origin main || git fetch --depth=1 origin master || echo "Failed to fetch updates"
        cd "$PROJECT_ROOT"
    else
        echo "Cloning repository..."
        # Use shallow clone to save bandwidth and time
        git clone --depth 1 --single-branch "${TEST_REPOS[$name]}" "$repo_path" || {
            echo "Failed to clone $name repository"
            continue
        }
    fi
    
    # Count LOC
    echo "Counting lines of code..."
    loc_count=$(count_loc "$repo_path")
    
    # Save LOC count
    echo "$loc_count" > "$repo_path-loc-count.txt"
    echo "Total LOC for $name: $loc_count"
    
    # Create metadata file
    cat > "$repo_path-metadata.json" <<EOF
{
    "name": "$name",
    "url": "${TEST_REPOS[$name]}",
    "loc_count": $loc_count,
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "meets_1m_requirement": $([ $loc_count -ge 1000000 ] && echo "true" || echo "false")
}
EOF
    
    # Verify 1M LOC requirement
    if [ $loc_count -ge 1000000 ]; then
        echo "✅ Meets 1M LOC requirement"
    else
        echo "⚠️  Below 1M LOC requirement (${loc_count} lines)"
    fi
done

# Create summary report
echo ""
echo "📊 Creating Summary Report..."
echo "============================"

cat > "$REPO_DIR/repository-summary.json" <<EOF
{
    "collection_timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "repositories": [
EOF

first=true
for name in "${!TEST_REPOS[@]}"; do
    if [ -f "$REPO_DIR/$name-metadata.json" ]; then
        if [ "$first" = false ]; then
            echo "," >> "$REPO_DIR/repository-summary.json"
        fi
        cat "$REPO_DIR/$name-metadata.json" >> "$REPO_DIR/repository-summary.json"
        first=false
    fi
done

cat >> "$REPO_DIR/repository-summary.json" <<EOF
    ],
    "total_repositories": $(ls -1 "$REPO_DIR" | grep -v ".txt\|.json" | wc -l),
    "meeting_1m_requirement": $(grep -l '"meets_1m_requirement": true' "$REPO_DIR"/*-metadata.json 2>/dev/null | wc -l)
}
EOF

# Display summary
echo ""
echo "✅ Repository Collection Complete!"
echo ""
echo "Summary:"
echo "--------"
for name in "${!TEST_REPOS[@]}"; do
    if [ -f "$REPO_DIR/$name-loc-count.txt" ]; then
        loc=$(cat "$REPO_DIR/$name-loc-count.txt")
        printf "%-15s: %'d LOC\n" "$name" "$loc"
    fi
done

echo ""
echo "Repository data saved to: $REPO_DIR"
echo "Summary report: $REPO_DIR/repository-summary.json"