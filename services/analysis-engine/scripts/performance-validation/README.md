# Performance Validation Scripts

## Emergency 1M LOC Performance Validation

This directory contains scripts and tools for validating the analysis-engine's claim of analyzing 1M lines of code in under 5 minutes.

### Phase 1: Repository Collection

#### Running the Repository Collection Script

The repository collection script will clone several large open-source repositories for testing:
- **rust-lang/rust**: ~2M LOC
- **torvalds/linux**: ~20M LOC  
- **tensorflow/tensorflow**: ~3M LOC
- **kubernetes/kubernetes**: ~2M LOC
- **chromium/chromium**: ~10M+ LOC

**WARNING**: These repositories are very large. The shallow clone will still download several GB of data.

To run the collection script:

```bash
cd services/analysis-engine
./scripts/performance-validation/collect-test-repositories.sh
```

The script will:
1. Create `test-data/large-repositories/` directory
2. Clone each repository with `--depth 1` to minimize download size
3. Count lines of code for each repository
4. Generate metadata files with LOC counts
5. Create a summary report

Expected runtime: 15-45 minutes depending on network speed.

### Phase 2: Running Validation Tests

After repositories are collected, run the validation tests:

```bash
cd services/analysis-engine
cargo test repository_validation -- --nocapture
```

This will verify that:
- Repositories meet the 1M LOC requirement
- Language diversity is sufficient for testing
- Repository characteristics are suitable for benchmarking

### Next Steps

Once repositories are collected and validated, proceed with:
1. Running performance benchmarks (see `benches/large_scale_analysis.rs`)
2. Executing Cloud Run performance tests
3. Collecting evidence for production readiness assessment

### Monitoring Progress

Check the collection progress by monitoring:
- `test-data/large-repositories/*/loc-count.txt` - LOC counts for each repo
- `test-data/large-repositories/repository-summary.json` - Overall summary

### Troubleshooting

If repository cloning fails:
1. Check network connectivity
2. Ensure sufficient disk space (need ~10GB free)
3. Try cloning individual repositories manually
4. Consider using a faster network connection

For very large repositories like chromium, you may want to exclude them initially and test with the smaller ones first.