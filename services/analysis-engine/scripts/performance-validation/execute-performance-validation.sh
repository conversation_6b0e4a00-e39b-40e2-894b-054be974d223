#!/bin/bash
# Execute Emergency Performance Validation Tests
# Agent 11B - Emergency Performance Validation
# This script validates the 1M LOC in <5 minutes claim

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=$(gcloud config get-value project)
SERVICE_NAME="analysis-engine-perf"
REGION="us-central1"
RESULTS_DIR="evidence/agent-11b"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create results directory
mkdir -p "$RESULTS_DIR"

echo -e "${BLUE}🚀 Starting Emergency Performance Validation${NC}"
echo "=========================================="
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"
echo "Timestamp: $TIMESTAMP"
echo ""

# Get service URL
if [ -n "$1" ]; then
    PERFORMANCE_SERVICE_URL="$1"
    echo "Using provided URL: $PERFORMANCE_SERVICE_URL"
else
    echo "Getting service URL..."
    PERFORMANCE_SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --platform=managed \
        --format='value(status.traffic[].url)' | grep perf-test | head -1)
    
    if [ -z "$PERFORMANCE_SERVICE_URL" ]; then
        echo -e "${RED}Error: Could not find performance test service URL${NC}"
        echo "Deploy the service first with: gcloud builds submit --config=cloudbuild-performance-test.yaml"
        exit 1
    fi
fi

echo "Service URL: $PERFORMANCE_SERVICE_URL"
echo ""

# Test repositories and their expected characteristics
declare -A TEST_REPOS=(
    ["kubernetes"]="https://github.com/kubernetes/kubernetes.git"
    ["rust"]="https://github.com/rust-lang/rust.git"
    ["tensorflow"]="https://github.com/tensorflow/tensorflow.git"
    ["linux"]="https://github.com/torvalds/linux.git"
)

declare -A EXPECTED_LOC=(
    ["kubernetes"]="1500000"
    ["rust"]="2500000"
    ["tensorflow"]="3500000"
    ["linux"]="20000000"
)

# Function to test service health
test_service_health() {
    echo -e "${YELLOW}Testing service health...${NC}"
    
    local health_response=$(curl -s -w "\n%{http_code}" -X GET "$PERFORMANCE_SERVICE_URL/health" \
        -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
        2>/dev/null)
    
    local http_code=$(echo "$health_response" | tail -1)
    local body=$(echo "$health_response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Service is healthy${NC}"
        echo "Response: $body"
    else
        echo -e "${RED}❌ Service health check failed (HTTP $http_code)${NC}"
        echo "Response: $body"
        exit 1
    fi
    echo ""
}

# Function to execute single repository test
test_repository() {
    local repo_name=$1
    local repo_url=$2
    local expected_loc=${EXPECTED_LOC[$repo_name]}
    
    echo -e "${BLUE}📦 Testing $repo_name repository...${NC}"
    echo "URL: $repo_url"
    echo "Expected LOC: ~$(printf "%'d" $expected_loc)"
    echo ""
    
    # Prepare request payload
    local request_payload=$(cat <<EOF
{
    "repository_url": "$repo_url",
    "branch": "main",
    "enable_patterns": true,
    "enable_embeddings": false,
    "timeout": 300,
    "performance_test": true
}
EOF
)
    
    # Save request for debugging
    echo "$request_payload" > "$RESULTS_DIR/request-${repo_name}-${TIMESTAMP}.json"
    
    # Start time measurement
    local start_time=$(date +%s)
    echo "Starting analysis at $(date)..."
    
    # Execute analysis
    local response=$(curl -s -w "\n%{http_code}" -X POST "$PERFORMANCE_SERVICE_URL/api/v1/analyze" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
        -d "$request_payload" \
        --max-time 360 \
        2>/dev/null)
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Parse response
    local http_code=$(echo "$response" | tail -1)
    local body=$(echo "$response" | head -n -1)
    
    # Save raw response
    echo "$body" > "$RESULTS_DIR/response-${repo_name}-${TIMESTAMP}.json"
    
    # Parse JSON response
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        local success=$(echo "$body" | jq -r '.success // false')
        local analysis_id=$(echo "$body" | jq -r '.data.analysis_id // "unknown"')
        local lines_analyzed=$(echo "$body" | jq -r '.data.metrics.total_lines // 0')
        local files_analyzed=$(echo "$body" | jq -r '.data.metrics.total_files // 0')
        local memory_peak=$(echo "$body" | jq -r '.data.metrics.memory_peak_mb // 0')
        local processing_time=$(echo "$body" | jq -r '.data.metrics.processing_time_ms // 0')
        
        # Create result summary
        local result_summary=$(cat <<EOF
{
    "repository": "$repo_name",
    "repository_url": "$repo_url",
    "analysis_id": "$analysis_id",
    "duration_seconds": $duration,
    "processing_time_ms": $processing_time,
    "lines_of_code": $lines_analyzed,
    "files_analyzed": $files_analyzed,
    "memory_peak_mb": $memory_peak,
    "success": $success,
    "meets_5min_target": $([ $duration -le 300 ] && echo "true" || echo "false"),
    "http_code": "$http_code",
    "timestamp": "$TIMESTAMP"
}
EOF
)
        echo "$result_summary" > "$RESULTS_DIR/benchmark-${repo_name}-${TIMESTAMP}.json"
        
        # Display results
        echo -e "${GREEN}Analysis completed!${NC}"
        echo "  Analysis ID: $analysis_id"
        echo "  Duration: ${duration}s"
        echo "  Lines Analyzed: $(printf "%'d" $lines_analyzed)"
        echo "  Files Analyzed: $(printf "%'d" $files_analyzed)"
        echo "  Memory Peak: ${memory_peak}MB"
        echo "  Processing Time: ${processing_time}ms"
        
        # Validate against 5-minute target
        if [ "$success" = "true" ] && [ $duration -le 300 ]; then
            echo -e "  ${GREEN}✅ PASSED: Under 5 minutes${NC}"
        else
            echo -e "  ${RED}❌ FAILED: Over 5 minutes or errored${NC}"
        fi
    else
        # Handle error response
        echo -e "${RED}Analysis failed with HTTP $http_code${NC}"
        echo "Response: $body"
        
        local error_summary=$(cat <<EOF
{
    "repository": "$repo_name",
    "repository_url": "$repo_url",
    "duration_seconds": $duration,
    "success": false,
    "error_message": "HTTP $http_code",
    "http_code": "$http_code",
    "timestamp": "$TIMESTAMP"
}
EOF
)
        echo "$error_summary" > "$RESULTS_DIR/benchmark-${repo_name}-${TIMESTAMP}.json"
    fi
    
    echo ""
    echo "---"
    echo ""
}

# Function to test concurrent analysis
test_concurrent_analysis() {
    echo -e "${YELLOW}🔄 Testing Concurrent Analysis Capability...${NC}"
    echo "Running 3 concurrent 1M LOC analyses..."
    
    local concurrent_start=$(date +%s)
    
    # Launch concurrent requests
    for i in {1..3}; do
        (
            local request_payload=$(cat <<EOF
{
    "repository_url": "https://github.com/kubernetes/kubernetes.git",
    "branch": "main",
    "enable_patterns": false,
    "enable_embeddings": false,
    "timeout": 300,
    "performance_test": true,
    "test_id": "concurrent-$i"
}
EOF
)
            curl -s -X POST "$PERFORMANCE_SERVICE_URL/api/v1/analyze" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
                -d "$request_payload" \
                --max-time 360 \
                > "$RESULTS_DIR/concurrent-$i-${TIMESTAMP}.json" 2>&1
        ) &
    done
    
    # Wait for all concurrent requests
    wait
    
    local concurrent_end=$(date +%s)
    local concurrent_duration=$((concurrent_end - concurrent_start))
    
    echo "Concurrent analysis completed in ${concurrent_duration}s"
    
    # Check results
    local all_success=true
    for i in {1..3}; do
        if [ -f "$RESULTS_DIR/concurrent-$i-${TIMESTAMP}.json" ]; then
            local success=$(jq -r '.success // false' "$RESULTS_DIR/concurrent-$i-${TIMESTAMP}.json")
            if [ "$success" != "true" ]; then
                all_success=false
            fi
        else
            all_success=false
        fi
    done
    
    if [ "$all_success" = "true" ] && [ $concurrent_duration -le 900 ]; then
        echo -e "${GREEN}✅ PASSED: 3 concurrent analyses completed successfully${NC}"
    else
        echo -e "${RED}❌ FAILED: Concurrent analysis failed or took too long${NC}"
    fi
    
    echo ""
}

# Main execution
main() {
    # Test service health first
    test_service_health
    
    # Test individual repositories
    echo -e "${BLUE}📊 Starting Individual Repository Tests${NC}"
    echo "======================================"
    echo ""
    
    for repo_name in "${!TEST_REPOS[@]}"; do
        test_repository "$repo_name" "${TEST_REPOS[$repo_name]}"
        
        # Add delay between tests to avoid overwhelming the service
        sleep 5
    done
    
    # Test concurrent analysis
    test_concurrent_analysis
    
    # Generate summary report
    echo -e "${BLUE}📈 Generating Summary Report...${NC}"
    
    # Count results
    local total_tests=$(ls -1 "$RESULTS_DIR"/benchmark-*-${TIMESTAMP}.json 2>/dev/null | wc -l)
    local passed_tests=$(grep -l '"meets_5min_target": true' "$RESULTS_DIR"/benchmark-*-${TIMESTAMP}.json 2>/dev/null | wc -l)
    local failed_tests=$((total_tests - passed_tests))
    
    # Find repositories that met 1M LOC requirement
    local met_1m_requirement=$(grep -l '"lines_of_code": [0-9]\{7,\}' "$RESULTS_DIR"/benchmark-*-${TIMESTAMP}.json 2>/dev/null | wc -l)
    
    echo ""
    echo -e "${BLUE}=============== VALIDATION SUMMARY ===============${NC}"
    echo "Total Tests: $total_tests"
    echo "Passed (< 5 min): $passed_tests"
    echo "Failed (> 5 min): $failed_tests"
    echo "Met 1M LOC requirement: $met_1m_requirement"
    
    if [ $met_1m_requirement -gt 0 ] && [ $passed_tests -eq $met_1m_requirement ]; then
        echo ""
        echo -e "${GREEN}🎉 VALIDATION RESULT: PASSED${NC}"
        echo -e "${GREEN}The 1M LOC in <5 minutes claim is VALIDATED!${NC}"
    else
        echo ""
        echo -e "${RED}❌ VALIDATION RESULT: FAILED${NC}"
        echo -e "${RED}The 1M LOC in <5 minutes claim is NOT validated.${NC}"
    fi
    
    echo ""
    echo "Evidence collected in: $RESULTS_DIR"
    echo "Timestamp: $TIMESTAMP"
    echo ""
    
    # Create final summary file
    local summary=$(cat <<EOF
{
    "validation_timestamp": "$TIMESTAMP",
    "total_tests": $total_tests,
    "passed_tests": $passed_tests,
    "failed_tests": $failed_tests,
    "met_1m_loc_requirement": $met_1m_requirement,
    "validation_passed": $([ $met_1m_requirement -gt 0 ] && [ $passed_tests -eq $met_1m_requirement ] && echo "true" || echo "false"),
    "service_url": "$PERFORMANCE_SERVICE_URL"
}
EOF
)
    echo "$summary" > "$RESULTS_DIR/validation-summary-${TIMESTAMP}.json"
    
    echo "Summary saved to: $RESULTS_DIR/validation-summary-${TIMESTAMP}.json"
}

# Run main execution
main