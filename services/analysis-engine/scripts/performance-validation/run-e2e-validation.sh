#!/bin/bash
# End-to-End Performance Validation Script
# Agent 11B - Emergency Performance Validation
# This script orchestrates the complete validation process

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../" && pwd)"
EVIDENCE_DIR="$PROJECT_ROOT/evidence/agent-11b"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$EVIDENCE_DIR/e2e-validation-${TIMESTAMP}.log"

# Create evidence directory
mkdir -p "$EVIDENCE_DIR"

# Logging function
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Function to check prerequisites
check_prerequisites() {
    log "${YELLOW}Checking prerequisites...${NC}"
    
    local all_good=true
    
    # Check Rust installation
    if command -v cargo &> /dev/null; then
        log "✅ Rust/Cargo installed: $(cargo --version)"
    else
        log "❌ Rust/Cargo not found"
        all_good=false
    fi
    
    # Check Docker
    if command -v docker &> /dev/null; then
        log "✅ Docker installed: $(docker --version)"
    else
        log "⚠️  Docker not found (optional for local testing)"
    fi
    
    # Check gcloud
    if command -v gcloud &> /dev/null; then
        log "✅ gcloud installed: $(gcloud --version | head -1)"
    else
        log "⚠️  gcloud not found (required for Cloud Run deployment)"
    fi
    
    # Check repository directory
    if [ -d "$PROJECT_ROOT/test-data/large-repositories" ]; then
        local repo_count=$(ls -1 "$PROJECT_ROOT/test-data/large-repositories" | grep -v ".txt\|.json" | wc -l)
        if [ $repo_count -gt 0 ]; then
            log "✅ Test repositories found: $repo_count"
        else
            log "❌ No test repositories found"
            all_good=false
        fi
    else
        log "❌ Test repository directory not found"
        all_good=false
    fi
    
    if [ "$all_good" = false ]; then
        log "${RED}Prerequisites check failed. Please resolve issues above.${NC}"
        exit 1
    fi
    
    log "${GREEN}All prerequisites satisfied!${NC}\n"
}

# Function to run local validation tests
run_local_validation() {
    log "${BLUE}🏠 Running Local Validation Tests${NC}"
    log "================================="
    
    # Build the project
    log "\n${YELLOW}Building analysis-engine...${NC}"
    cd "$PROJECT_ROOT"
    cargo build --release --bin analysis-engine 2>&1 | tee -a "$LOG_FILE"
    
    # Run repository validation tests
    log "\n${YELLOW}Running repository validation tests...${NC}"
    cargo test repository_validation -- --ignored --nocapture 2>&1 | tee -a "$LOG_FILE" || true
    
    # Run performance validation suite
    log "\n${YELLOW}Running performance validation suite...${NC}"
    cargo test --test performance_validation_suite -- --ignored --nocapture 2>&1 | tee -a "$LOG_FILE" || true
    
    # Run local benchmarks (quick sample)
    log "\n${YELLOW}Running performance benchmarks (sample)...${NC}"
    cargo bench --bench large_scale_analysis -- --sample-size 2 2>&1 | tee -a "$LOG_FILE" || true
    
    log "${GREEN}Local validation tests completed!${NC}\n"
}

# Function to deploy to Cloud Run
deploy_to_cloud_run() {
    log "${BLUE}☁️  Deploying to Cloud Run${NC}"
    log "========================"
    
    if ! command -v gcloud &> /dev/null; then
        log "${YELLOW}Skipping Cloud Run deployment (gcloud not available)${NC}"
        return
    fi
    
    # Check if user wants to deploy
    read -p "Deploy to Cloud Run for production-equivalent testing? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "${YELLOW}Skipping Cloud Run deployment${NC}"
        return
    fi
    
    # Set up cloud resources
    log "\n${YELLOW}Setting up cloud resources...${NC}"
    "$SCRIPT_DIR/setup-cloud-resources.sh" 2>&1 | tee -a "$LOG_FILE"
    
    # Deploy service
    log "\n${YELLOW}Deploying performance test service...${NC}"
    cd "$PROJECT_ROOT"
    gcloud builds submit --config=cloudbuild-performance-test.yaml 2>&1 | tee -a "$LOG_FILE"
    
    log "${GREEN}Cloud Run deployment completed!${NC}\n"
}

# Function to run Cloud Run validation
run_cloud_validation() {
    log "${BLUE}🚀 Running Cloud Run Validation${NC}"
    log "==============================="
    
    if ! command -v gcloud &> /dev/null; then
        log "${YELLOW}Skipping Cloud Run validation (gcloud not available)${NC}"
        return
    fi
    
    # Check if service is deployed
    local service_url=$(gcloud run services describe analysis-engine-perf \
        --region=us-central1 \
        --platform=managed \
        --format='value(status.traffic[].url)' 2>/dev/null | grep perf-test | head -1)
    
    if [ -z "$service_url" ]; then
        log "${YELLOW}Cloud Run service not deployed. Skipping cloud validation.${NC}"
        return
    fi
    
    # Run performance validation
    log "\n${YELLOW}Executing performance validation against Cloud Run...${NC}"
    "$SCRIPT_DIR/execute-performance-validation.sh" "$service_url" 2>&1 | tee -a "$LOG_FILE"
    
    log "${GREEN}Cloud Run validation completed!${NC}\n"
}

# Function to analyze results
analyze_results() {
    log "${BLUE}📊 Analyzing Validation Results${NC}"
    log "==============================="
    
    # Run performance analyzer
    if [ -f "$EVIDENCE_DIR"/benchmark-*.json ]; then
        log "\n${YELLOW}Running performance analyzer...${NC}"
        cd "$PROJECT_ROOT"
        cargo run --release --bin performance_analyzer 2>&1 | tee -a "$LOG_FILE"
    else
        log "${YELLOW}No benchmark results found to analyze${NC}"
    fi
    
    # Collect evidence
    log "\n${YELLOW}Collecting validation evidence...${NC}"
    cargo run --release --bin evidence_collector 2>&1 | tee -a "$LOG_FILE"
    
    local exit_code=$?
    
    log "${GREEN}Analysis completed!${NC}\n"
    
    return $exit_code
}

# Function to generate final report
generate_final_report() {
    log "${BLUE}📝 Generating Final Report${NC}"
    log "========================="
    
    local report_file="$EVIDENCE_DIR/final-validation-report-${TIMESTAMP}.md"
    
    cat > "$report_file" <<EOF
# Emergency Performance Validation - Final Report

**Date**: $(date)  
**Agent**: 11B  
**Validation Type**: 1M LOC in <5 minutes claim  

## Validation Summary

EOF

    # Check for validation evidence
    if [ -f "$EVIDENCE_DIR"/validation-evidence-*.json ]; then
        local latest_evidence=$(ls -t "$EVIDENCE_DIR"/validation-evidence-*.json | head -1)
        local claim_validated=$(jq -r '.attestation.claim_validated' "$latest_evidence")
        local recommendation=$(jq -r '.attestation.recommendation' "$latest_evidence")
        
        if [ "$claim_validated" = "true" ]; then
            cat >> "$report_file" <<EOF
### Result: ✅ PASSED

The analysis-engine successfully validates its core performance claim of analyzing 1M lines of code in under 5 minutes.

**Recommendation**: $recommendation

EOF
        else
            cat >> "$report_file" <<EOF
### Result: ❌ FAILED

The analysis-engine does NOT meet the claimed performance target of 1M LOC in <5 minutes.

**Recommendation**: $recommendation

EOF
        fi
    else
        cat >> "$report_file" <<EOF
### Result: ⚠️ INCOMPLETE

Validation could not be completed. Check logs for details.

EOF
    fi
    
    # Add evidence location
    cat >> "$report_file" <<EOF
## Evidence Location

- **Log File**: \`$LOG_FILE\`
- **Benchmark Results**: \`$EVIDENCE_DIR/benchmark-*.json\`
- **Performance Reports**: \`$EVIDENCE_DIR/performance-validation-*.md\`
- **Evidence Collection**: \`$EVIDENCE_DIR/validation-evidence-*.json\`

## Next Steps

EOF
    
    if [ "$claim_validated" = "true" ]; then
        cat >> "$report_file" <<EOF
1. ✅ Proceed with production deployment
2. ✅ Update documentation with validated performance metrics
3. ✅ Configure production monitoring based on observed characteristics
4. ✅ Set up performance regression testing

EOF
    else
        cat >> "$report_file" <<EOF
1. ❌ HALT production deployment
2. ❌ Update customer communications with accurate performance claims
3. ❌ Implement performance optimizations
4. ❌ Re-run validation after optimizations

EOF
    fi
    
    cat >> "$report_file" <<EOF
---
Generated by Agent 11B - Emergency Performance Validation
EOF
    
    log "\n${GREEN}Final report saved to: $report_file${NC}"
    
    # Display report
    cat "$report_file"
}

# Main execution
main() {
    log "${BLUE}🎯 End-to-End Performance Validation${NC}"
    log "===================================="
    log "Timestamp: $TIMESTAMP"
    log "Log file: $LOG_FILE\n"
    
    # Step 1: Check prerequisites
    check_prerequisites
    
    # Step 2: Run local validation
    run_local_validation
    
    # Step 3: Deploy to Cloud Run (optional)
    deploy_to_cloud_run
    
    # Step 4: Run Cloud Run validation
    run_cloud_validation
    
    # Step 5: Analyze results
    analyze_results
    local validation_result=$?
    
    # Step 6: Generate final report
    generate_final_report
    
    # Final status
    log "\n${BLUE}===============================================${NC}"
    if [ $validation_result -eq 0 ]; then
        log "${GREEN}✅ END-TO-END VALIDATION PASSED${NC}"
        log "${GREEN}The 1M LOC in <5 minutes claim is VALIDATED!${NC}"
        exit 0
    else
        log "${RED}❌ END-TO-END VALIDATION FAILED${NC}"
        log "${RED}The 1M LOC in <5 minutes claim is NOT validated.${NC}"
        exit 1
    fi
}

# Run main execution
main