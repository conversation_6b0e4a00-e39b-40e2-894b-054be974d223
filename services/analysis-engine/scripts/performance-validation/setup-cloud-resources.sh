#!/bin/bash
# Set up Cloud Resources for Performance Testing
# Agent 11B - Emergency Performance Validation

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Setting up Cloud Resources for Performance Testing${NC}"
echo "================================================="

# Check if gcloud is configured
if ! gcloud config get-value project &>/dev/null; then
    echo -e "${RED}Error: gcloud is not configured. Run 'gcloud init' first.${NC}"
    exit 1
fi

PROJECT_ID=$(gcloud config get-value project)
SERVICE_NAME="analysis-engine-perf"
REGION="us-central1"

echo "Project ID: $PROJECT_ID"
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"
echo ""

# Step 1: Create service account for performance testing
echo -e "${YELLOW}Step 1: Creating service account...${NC}"
if gcloud iam service-accounts describe "${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
    echo "Service account already exists"
else
    gcloud iam service-accounts create "$SERVICE_NAME" \
        --display-name="Analysis Engine Performance Testing" \
        --description="Service account for performance testing of analysis engine"
    echo -e "${GREEN}✅ Service account created${NC}"
fi

# Step 2: Grant necessary permissions
echo -e "${YELLOW}Step 2: Granting permissions...${NC}"

# Cloud Run permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/run.invoker" \
    --quiet

# Spanner permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/spanner.databaseUser" \
    --quiet

# Cloud Storage permissions (for test data)
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer" \
    --quiet

# Secret Manager permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet

# Logging permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/logging.logWriter" \
    --quiet

echo -e "${GREEN}✅ Permissions granted${NC}"

# Step 3: Create secrets if they don't exist
echo -e "${YELLOW}Step 3: Setting up secrets...${NC}"

# JWT Secret
if ! gcloud secrets describe jwt-secret &>/dev/null; then
    echo "Creating JWT secret..."
    # Generate a secure random secret
    openssl rand -base64 32 | gcloud secrets create jwt-secret --data-file=-
    echo -e "${GREEN}✅ JWT secret created${NC}"
else
    echo "JWT secret already exists"
fi

# GitHub Token (placeholder - user needs to update)
if ! gcloud secrets describe github-token &>/dev/null; then
    echo "Creating GitHub token secret..."
    echo "PLACEHOLDER_GITHUB_TOKEN" | gcloud secrets create github-token --data-file=-
    echo -e "${YELLOW}⚠️  GitHub token created with placeholder. Update with actual token:${NC}"
    echo "  echo 'YOUR_ACTUAL_TOKEN' | gcloud secrets versions add github-token --data-file=-"
else
    echo "GitHub token secret already exists"
fi

# Grant service account access to secrets
gcloud secrets add-iam-policy-binding jwt-secret \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet

gcloud secrets add-iam-policy-binding github-token \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet

# Step 4: Check VPC connector
echo -e "${YELLOW}Step 4: Checking VPC connector...${NC}"
if gcloud compute networks vpc-access connectors describe ccl-vpc-connector \
    --region="$REGION" &>/dev/null; then
    echo -e "${GREEN}✅ VPC connector exists${NC}"
else
    echo -e "${YELLOW}⚠️  VPC connector 'ccl-vpc-connector' not found${NC}"
    echo "To create a VPC connector, run:"
    echo "  gcloud compute networks vpc-access connectors create ccl-vpc-connector \\"
    echo "    --region=$REGION \\"
    echo "    --subnet=ccl-subnet \\"
    echo "    --subnet-project=$PROJECT_ID"
fi

# Step 5: Create Cloud Storage bucket for artifacts
echo -e "${YELLOW}Step 5: Setting up Cloud Storage bucket...${NC}"
BUCKET_NAME="${PROJECT_ID}-performance-artifacts"

if gsutil ls -b "gs://$BUCKET_NAME" &>/dev/null; then
    echo "Bucket already exists"
else
    gsutil mb -p "$PROJECT_ID" -l "$REGION" "gs://$BUCKET_NAME"
    echo -e "${GREEN}✅ Storage bucket created${NC}"
fi

# Step 6: Enable required APIs
echo -e "${YELLOW}Step 6: Enabling required APIs...${NC}"
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    secretmanager.googleapis.com \
    spanner.googleapis.com \
    redis.googleapis.com \
    vpcaccess.googleapis.com \
    --quiet

echo -e "${GREEN}✅ APIs enabled${NC}"

# Step 7: Create build trigger (optional)
echo -e "${YELLOW}Step 7: Build trigger setup...${NC}"
echo "To create a build trigger for automatic performance testing:"
echo "  gcloud builds triggers create github \\"
echo "    --repo-name=episteme \\"
echo "    --repo-owner=YOUR_GITHUB_ORG \\"
echo "    --branch-pattern=^perf-test-.*$ \\"
echo "    --build-config=services/analysis-engine/cloudbuild-performance-test.yaml"

echo ""
echo -e "${GREEN}🎉 Cloud resources setup complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Update GitHub token if needed:"
echo "   echo 'YOUR_ACTUAL_TOKEN' | gcloud secrets versions add github-token --data-file=-"
echo ""
echo "2. Deploy the performance test service:"
echo "   cd services/analysis-engine"
echo "   gcloud builds submit --config=cloudbuild-performance-test.yaml"
echo ""
echo "3. Run performance validation:"
echo "   ./scripts/performance-validation/execute-performance-validation.sh"