src/services/analyzer/mod.rs:84:                .map_err(|e| anyhow::anyhow!("Failed to create TreeSitter parser: {e}"))?,
src/services/analyzer/events.rs:168:                Err(e) => results.push(Err(anyhow::anyhow!("Task join error: {e}"))),
src/main.rs:39:                .map_err(|e| anyhow::anyhow!("Failed to create env filter: {e}"))?,
src/backpressure/mod.rs:472:            return Err(anyhow::anyhow!("Memory pressure detected: {memory_usage}MB > {max_memory}MB",
src/backpressure/mod.rs:477:            return Err(anyhow::anyhow!("CPU pressure detected: {cpu_usage}% > {cpu_threshold}%", 
