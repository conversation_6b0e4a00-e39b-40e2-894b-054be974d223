#!/usr/bin/env python3
"""Categorize tracing macros for safety assessment."""

import re
from collections import defaultdict

def categorize_tracing(line):
    """Categorize a tracing macro line based on safety for modernization."""
    # Check for various patterns
    # 1. Simple variables only (SAFE)
    if re.search(r'"\s*[^{]*\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\s*[^{]*"', line) and \
       not re.search(r'\.[a-zA-Z_]|::|->|\(|\)|&|\*|!|\?', line):
        return "safe_simple_var", "Simple variable only"
    
    # 2. Field access (KEEP AS-IS)
    if re.search(r'\.[a-zA-Z_][a-zA-Z0-9_]*', line):
        return "keep_field_access", "Contains field access"
    
    # 3. Method calls (KEEP AS-IS)
    if re.search(r'\.[a-zA-Z_][a-zA-Z0-9_]*\s*\(', line):
        return "keep_method_call", "Contains method call"
    
    # 4. Debug formatting {:?} (KEEP AS-IS for now)
    if re.search(r'\{[^}]*:[\?#x]', line):
        return "keep_debug_format", "Contains debug/hex/special formatting"
    
    # 5. Named parameters (Check if simple)
    if re.search(r'\{[a-zA-Z_][a-zA-Z0-9_]*\}.*,\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=', line):
        # Check if the named param is a simple variable
        if not re.search(r'=\s*[^,\s]+\.[a-zA-Z_]', line):
            return "safe_named_param", "Simple named parameters"
        return "keep_named_complex", "Complex named parameters"
    
    return "other", "Uncategorized pattern"

def main():
    categories = defaultdict(list)
    
    with open('evidence/agent-02/audit-tracing-remaining.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            category, reason = categorize_tracing(line)
            categories[category].append((line, reason))
    
    # Print summary
    print("\nTRACING MACRO CATEGORIZATION SUMMARY")
    print("=" * 80)
    print()
    
    total = sum(len(items) for items in categories.values())
    
    for category, items in sorted(categories.items()):
        count = len(items)
        percentage = (count / total) * 100 if total > 0 else 0
        print(f"{category}: {count} ({percentage:.1f}%)")
    
    print(f"\nTotal: {total}")
    print("=" * 80)
    
    # Show safe examples
    print("\nSAFE TO MODERNIZE:")
    print("-" * 80)
    
    safe_categories = ["safe_simple_var", "safe_named_param"]
    safe_count = 0
    for category in safe_categories:
        if category in categories:
            for line, reason in categories[category]:
                print(f"  {line[:120]}...")
                safe_count += 1
    
    print(f"\nTotal safe to modernize: {safe_count}")

if __name__ == "__main__":
    main()