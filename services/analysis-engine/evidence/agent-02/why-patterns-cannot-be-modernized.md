# Why Most Format Strings Cannot Be Modernized

## The Problem

The `uninlined_format_args` clippy lint suggests converting:
```rust
format!("Hello {}", name)  // Old style
```
to:
```rust
format!("Hello {name}")    // New style
```

However, this is **ONLY safe for simple variable references**.

## Unsafe Modernization Examples

### 1. Field Access (36.2% of format! macros)
```rust
// UNSAFE to modernize:
format!("Error: {}", obj.field)  // Current
format!("Error: {obj.field}")    // Would break - field access not allowed

// Real example from codebase:
format!("Error: {error_message}", error_message = failed_file.error_message)
```

### 2. Method Calls (Common in tracing)
```rust
// UNSAFE to modernize:
format!("{:?}", status.value())  // Current
format!("{status.value()}")      // Would break - method calls not allowed

// Real example:
tracing::warn!("Failed to connect: {}", e.to_string())
```

### 3. Debug Formatting (4.1% of format! macros)
```rust
// UNSAFE to modernize:
format!("{:?}", pattern.pattern_type)  // Current
format!("{pattern.pattern_type:?}")    // Different syntax, risky to automate

// The :? formatter requires special handling
```

### 4. Complex Expressions (4.9% of format! macros)
```rust
// UNSAFE to modernize:
format!("/proc/{}/status", pid)        // Current
format!("/proc/{pid}/status")          // Safe, but...
format!("{} minutes", secs / 60)       // Current
format!("{secs / 60} minutes")        // Would break - expressions not allowed
```

### 5. References and Slices
```rust
// UNSAFE to modernize:
format!("{}***", &secret[..2])         // Current
format!("{&secret[..2]}***")           // Would break - slice syntax not allowed
```

## The Clippy Lint Limitation

The `uninlined_format_args` lint does not distinguish between:
- Simple variables (safe to inline)
- Field access (unsafe to inline)
- Method calls (unsafe to inline)
- Complex expressions (unsafe to inline)

This leads to **~95% false positives** in our codebase.

## Real Impact

From our audit of 442 format strings:
- **Only 13 (2.9%)** are truly safe to modernize
- **429 (97.1%)** would break if modernized

The lint is generating noise without providing value in this codebase.

## Recommendation

1. **Suppress the lint** project-wide or in specific modules
2. **Document the decision** in the project's clippy.toml
3. **Manually modernize** only the 13 safe instances
4. **Focus on real issues** like the security vulnerabilities

## Example Suppression

```rust
// At module level:
#![allow(clippy::uninlined_format_args)]

// Or in clippy.toml:
[[disallowed-lints]]
uninlined-format-args = "allow"
```

This would eliminate 163 false warnings and let us focus on actual code quality issues.