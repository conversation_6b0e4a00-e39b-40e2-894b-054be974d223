#!/usr/bin/env python3
"""Categorize format strings for safety assessment."""

import re
from collections import defaultdict

def categorize_format_string(line):
    """Categorize a format string line based on safety for modernization."""
    # Extract the format! macro content
    match = re.search(r'format!\((.*)\)', line)
    if not match:
        return "unknown", "Cannot parse format! macro"
    
    content = match.group(1)
    
    # Check for various patterns
    # 1. Simple variables only (SAFE)
    if re.search(r'"\s*[^{]*\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\s*[^{]*"', content) and \
       not re.search(r'\.[a-zA-Z_]|::|->|\(|\)|&|\*|!|\?', content):
        return "safe_simple_var", "Simple variable only"
    
    # 2. Field access (KEEP AS-IS)
    if re.search(r'\.[a-zA-Z_][a-zA-Z0-9_]*', content):
        return "keep_field_access", "Contains field access"
    
    # 3. Method calls (KEEP AS-IS)
    if re.search(r'\.[a-zA-Z_][a-zA-Z0-9_]*\s*\(', content):
        return "keep_method_call", "Contains method call"
    
    # 4. Debug formatting {:?} (KEEP AS-IS for now)
    if re.search(r'\{[^}]*:[\?#x]', content):
        return "keep_debug_format", "Contains debug/hex/special formatting"
    
    # 5. Path-like patterns (KEEP AS-IS)
    if re.search(r'::', content):
        return "keep_path_pattern", "Contains :: path separator"
    
    # 6. References or pointers (KEEP AS-IS)
    if re.search(r'[&\*]', content):
        return "keep_reference", "Contains reference/pointer"
    
    # 7. Function calls (KEEP AS-IS)
    if re.search(r'[a-zA-Z_][a-zA-Z0-9_]*\s*\(', content) and 'format!' not in content:
        return "keep_function_call", "Contains function call"
    
    # 8. Named parameters (POTENTIALLY SAFE)
    if re.search(r'\{[a-zA-Z_][a-zA-Z0-9_]*\}.*,\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=', content):
        return "named_param", "Uses named parameters"
    
    # 9. Complex expressions (KEEP AS-IS)
    if re.search(r'[+\-*/|&^%<>!=]', content):
        return "keep_expression", "Contains complex expression"
    
    return "other", "Uncategorized pattern"

def main():
    categories = defaultdict(list)
    
    with open('audit-format-remaining.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            category, reason = categorize_format_string(line)
            categories[category].append((line, reason))
    
    # Print summary
    print("FORMAT STRING CATEGORIZATION SUMMARY")
    print("=" * 80)
    print()
    
    total = sum(len(items) for items in categories.values())
    
    for category, items in sorted(categories.items()):
        count = len(items)
        percentage = (count / total) * 100 if total > 0 else 0
        print(f"{category}: {count} ({percentage:.1f}%)")
    
    print(f"\nTotal: {total}")
    print("=" * 80)
    
    # Detailed breakdown
    print("\nDETAILED BREAKDOWN BY CATEGORY")
    print("=" * 80)
    
    for category, items in sorted(categories.items()):
        print(f"\n{category.upper()} ({len(items)} items):")
        print("-" * 80)
        for line, reason in items[:5]:  # Show first 5 examples
            print(f"  {reason}")
            print(f"    {line[:120]}...")
        if len(items) > 5:
            print(f"  ... and {len(items) - 5} more")

if __name__ == "__main__":
    main()