src/contracts.rs:233:                    text: Some(format!("Error: {error_message}", error_message = failed_file.error_message)),
src/contracts.rs:268:                pattern_type: format!("{pattern_type:?}", pattern_type = pattern.pattern_type),
src/contracts.rs:416:        chunk_type: format!("{chunk_type:?}", chunk_type = chunk.chunk_type),
src/contracts.rs:429:        symbol_type: format!("{symbol_type:?}", symbol_type = symbol.symbol_type),
src/contracts.rs:442:        visibility: symbol.visibility.as_ref().map(|v| format!("{v:?}")),
src/api/handlers/websocket.rs:269:                            stage: format!("{status:?}", status = status.value()),
src/services/pattern_detector.rs:164:                format!("{current_class:?}::{name}", current_class = self.current_class),
src/migrations/mod.rs:88:                    .with_context(|| format!("Failed to read migration file: {:?}", path))?;
src/migrations/mod.rs:160:                applied_list.push(format!("{}: {}", migration.version, migration.description));
src/migrations/mod.rs:162:                pending_list.push(format!("{}: {}", migration.version, migration.description));
src/services/semantic_search.rs:264:                search_strategy: format!("{:?}", query.search_strategy),
src/services/semantic_search.rs:286:                format!("{:x}", hasher.finish())
src/services/semantic_search.rs:460:                    preview.push_str(&format!("{}: {}\n", line_num + 1, line));
src/services/semantic_search.rs:465:            format!("File: {}", analysis.path)
src/services/semantic_search.rs:478:                        symbol_type: format!("{:?}", symbol.symbol_type),
src/services/semantic_search.rs:536:        format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
src/services/semantic_search.rs:575:                search_strategy: format!("{:?}", query.search_strategy),
src/services/semantic_search.rs:599:        format!("search_{:x}", hasher.finish())
src/services/embeddings.rs:279:                    chunk_id: format!("chunk_{i:016x}"),
src/services/embeddings.rs:362:        content.push_str(&format!("File: {path}\n", path = analysis.path));
src/services/embeddings.rs:365:        content.push_str(&format!("Language: {language}\n", language = analysis.language));
src/services/embeddings.rs:371:                content.push_str(&format!("- {symbol_type} {name}\n", symbol_type = symbol.symbol_type, name = symbol.name));
src/api/handlers/analysis.rs:87:                    format!("Service {service} temporarily unavailable"),
src/api/handlers/analysis.rs:144:        progress_url: Some(format!("/ws/analysis/{analysis_id}")),
src/services/security/risk/assessor.rs:97:                format!("Found {total_vulns} total vulnerabilities"),
src/services/security/risk/assessor.rs:98:                format!("Found {total_secrets} secrets"),
src/storage/pubsub.rs:35:            let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
src/storage/pubsub.rs:186:                        warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
src/parser/ast/chunk_extractor.rs:94:        let chunk_id = format!("chunk_{:016x}", counter);
src/parser/ast/chunk_extractor.rs:123:            Some(format!("{} {}", node.node_type, name))
src/services/code_quality_assessor.rs:406:        prompt.push_str(&format!("Total files: {}\n", repository_metrics.total_files));
src/services/code_quality_assessor.rs:407:        prompt.push_str(&format!("Total lines: {}\n", repository_metrics.total_lines));
src/services/code_quality_assessor.rs:408:        prompt.push_str(&format!("Average complexity: {:.2}\n", repository_metrics.average_complexity.unwrap_or(0.0)));
src/services/code_quality_assessor.rs:409:        prompt.push_str(&format!("Maintainability score: {:.2}\n", repository_metrics.maintainability_score.unwrap_or(0.0)));
src/services/code_quality_assessor.rs:412:            prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
src/services/code_quality_assessor.rs:418:            prompt.push_str(&format!("File: {}\n", analysis.path));
src/services/code_quality_assessor.rs:419:            prompt.push_str(&format!("Language: {}\n", analysis.language));
src/services/code_quality_assessor.rs:420:            prompt.push_str(&format!("Lines: {}\n", analysis.metrics.lines_of_code));
src/services/code_quality_assessor.rs:421:            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));
src/services/code_quality_assessor.rs:422:            prompt.push_str(&format!("Maintainability: {:.2}\n", analysis.metrics.maintainability_index));
src/services/code_quality_assessor.rs:427:                    prompt.push_str(&format!("{} ", symbol.name));
src/services/code_quality_assessor.rs:434:                prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
src/services/code_quality_assessor.rs:439:            prompt.push_str(&format!("... and {} more files\n", analyses.len() - 10));
src/metrics/mod.rs:594:            let status_path = format!("/proc/{}/status", pid);
src/storage/storage.rs:27:        let object_name = format!("analysis_results/{}.json", analysis.id);
src/storage/storage.rs:46:        Ok(format!("gs://{}/{}", self.bucket_name, object_name))
src/services/security/dependency/parsers/gradle.rs:64:                let name = format!("{}:{}", group, artifact);
src/services/security/dependency/parsers/gradle.rs:93:                    let name = format!("{}:{}", parts[0], parts[1]);
src/services/embeddings_enhancement.rs:323:                    chunk_id: format!("chunk_{:016x}", i),
src/services/embeddings_enhancement.rs:367:            chunk_id: format!("fallback_chunk_{:016x}", idx),
src/services/embeddings_enhancement.rs:381:        content.push_str(&format!("File: {}\n", analysis.path));
src/services/embeddings_enhancement.rs:384:        content.push_str(&format!("Language: {}\n", analysis.language));
src/services/embeddings_enhancement.rs:416:                    content.push_str(&format!("{} ", class.name));
src/services/embeddings_enhancement.rs:424:                    content.push_str(&format!("{} ", func.name));
src/services/embeddings_enhancement.rs:432:                    content.push_str(&format!("{} ", item.name));
src/services/embeddings_enhancement.rs:442:            content.push_str(&format!("\nCode:\n{}", preview));
src/storage/gcp_clients.rs:183:        let topic_path = format!("projects/{project_id}/topics/{topic_name}", project_id = gcp_settings.project_id);
src/services/security/dependency/parsers/python.rs:49:                    format!("{}{}", op, version)
src/services/analyzer/storage.rs:120:                            pattern_statement.add_param("pattern_type", &format!("{:?}", pattern.pattern_type));
src/services/security/dependency/parsers/go.rs:69:                        current_version: format!("v{}", version),
src/services/security/dependency/parsers/go.rs:96:                        current_version: format!("v{}", version),
src/services/security/dependency/parsers/go.rs:146:                let key = format!("{}@{}", module, version);
src/services/security/vulnerability/detector.rs:136:                        description: format!("Potentially dangerous function call: {}", name),
src/services/intelligent_documentation.rs:708:        prompt.push_str(&format!("Repository URL: {}\n", repository_url));
src/services/intelligent_documentation.rs:709:        prompt.push_str(&format!("Primary language: {}\n", primary_language));
src/services/intelligent_documentation.rs:710:        prompt.push_str(&format!("Files analyzed: {}\n", analyses.len()));
src/services/intelligent_documentation.rs:719:                prompt.push_str(&format!("\nFile: {}\n", analysis.path));
src/services/intelligent_documentation.rs:725:                            prompt.push_str(&format!("  Function: {} ({})\n", symbol.name, symbol.signature.as_ref().unwrap_or(&"No signature".to_string())));
src/services/intelligent_documentation.rs:729:                            prompt.push_str(&format!("  Class: {}\n", symbol.name));
src/services/intelligent_documentation.rs:732:                            prompt.push_str(&format!("  Type: {}\n", symbol.name));
src/services/intelligent_documentation.rs:735:                            prompt.push_str(&format!("  Constant: {}\n", symbol.name));
src/services/intelligent_documentation.rs:748:                prompt.push_str(&format!("\nFile: {}\n```{}\n{}\n```\n", 
src/services/intelligent_documentation.rs:856:                description: format!("Module containing {} code", analysis.language),
src/services/intelligent_documentation.rs:870:                                description: format!("Function {}", symbol.name),
src/services/intelligent_documentation.rs:885:                                description: format!("Class {}", symbol.name),
src/services/intelligent_documentation.rs:901:                                description: format!("Type {}", symbol.name),
src/services/intelligent_documentation.rs:914:                                description: format!("Constant {}", symbol.name),
src/services/intelligent_documentation.rs:930:                description: format!("{} repository written in {}", repo_name, primary_language),
src/parser/streaming/hasher.rs:24:        format!("{:x}", result)
src/parser/streaming/hasher.rs:31:        format!("{:x}", hasher.finalize())
src/api/auth_extractor.rs:246:            message: format!("Failed to get database connection: {:?}", e),
src/api/auth_extractor.rs:483:        .map_err(|e| format!("Database error: {}", e))?;
src/api/auth_extractor.rs:488:        .map_err(|e| format!("Query error: {}", e))?;
src/api/auth_extractor.rs:493:        .map_err(|e| format!("Read error: {}", e))?
src/api/auth_extractor.rs:497:            .map_err(|e| format!("Failed to get user_id: {}", e))?;
src/api/auth_extractor.rs:500:            .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
src/api/auth_extractor.rs:503:            .map_err(|e| format!("Failed to get salt: {}", e))?;
src/api/auth_extractor.rs:506:            .map_err(|e| format!("Failed to get rate_limit: {}", e))?;
src/api/auth_extractor.rs:523:        .map_err(|e| format!("Failed to decode salt: {}", e))?;
src/api/auth_extractor.rs:581:    format!("{:x}", hasher.finalize())
src/api/auth_extractor.rs:597:            "auth_method": format!("{:?}", auth_method),
src/api/auth_extractor.rs:617:            "auth_method": format!("{:?}", auth_method),
src/services/security/dependency/parsers/maven.rs:71:                                current_dependency.name = format!("{}:{}", current_dependency.name, text);
src/services/analyzer/streaming_processor.rs:224:                message: format!("Failed to get file metadata: {}", e),
src/services/analyzer/streaming_processor.rs:240:                message: format!("Failed to open file: {}", e),
src/services/analyzer/streaming_processor.rs:253:                message: format!("Failed to read file chunk: {}", e),
src/services/analyzer/streaming_processor.rs:295:                message: format!("Failed to open file: {}", e),
src/services/analyzer/streaming_processor.rs:341:            format!("{:x}", hasher.finalize())
src/services/analyzer/streaming_processor.rs:366:                text: Some(format!("Large file with {} lines", line_count)),
src/services/security/threat/modeler.rs:84:            let key = format!("{:?}", vuln.vulnerability_type);
src/services/security/threat/modeler.rs:102:                threat_name: format!("{} Exploitation Threat", vuln_type),
src/services/security/threat/modeler.rs:109:                asset_affected: Some(format!("{} files", vulns.len())),
src/services/security/threat/modeler.rs:155:                threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
src/services/security/threat/modeler.rs:163:                asset_affected: Some(format!("{} dependencies", vulns.len())),
src/services/analyzer/repository.rs:89:                    format!("Failed to get remote commit hash: {}", e),
src/api/rate_limit_extractor.rs:68:                format!("Rate limit check failed: {}", e),
src/api/rate_limit_extractor.rs:173:    let key = format!("rate_limit:{}", user_id);
src/api/rate_limit_extractor.rs:190:    let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {}", e))?;
src/api/rate_limit_extractor.rs:194:        let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {}", e))?;
src/api/rate_limit_extractor.rs:248:        format!("{} seconds", seconds_until_reset)
src/api/rate_limit_extractor.rs:250:        format!("{} minutes", seconds_until_reset / 60)
src/api/rate_limit_extractor.rs:252:        format!("{} hours", seconds_until_reset / 3600)
src/parser/adapters.rs:183:                                name: format!("{}.{}", name, attr_name),
src/parser/adapters.rs:209:                        message: format!("XML parse error: {}", e),
src/parser/adapters.rs:241:                message: format!("TOML parse error: {}", e),
src/parser/adapters.rs:255:                    format!("{}.{}", prefix, key)
src/parser/adapters.rs:353:                        name: format!("code_block_{}", lang),
src/parser/adapters.rs:369:                        documentation: Some(format!("Code block in {}", lang)),
src/parser/adapters.rs:420:                    signature: Some(format!("H{} {}", level, title)),
src/parser/adapters.rs:421:                    documentation: Some(format!("Heading level {} in documentation", level)),
src/parser/adapters.rs:433:                    name: format!("task: {}", task_text),
src/parser/adapters.rs:448:                    signature: Some(format!("Task: {} ({})", task_text, if checked { "completed" } else { "pending" })),
src/parser/adapters.rs:460:                    name: format!("list_item: {}", item_text),
src/parser/adapters.rs:475:                    signature: Some(format!("List item: {}", item_text)),
src/parser/adapters.rs:485:                        name: format!("table_row_{}", current_line),
src/parser/adapters.rs:500:                        signature: Some(format!("Table row with {} columns", cells.len())),
src/parser/adapters.rs:512:                    name: format!("link: {}", link_text),
src/parser/adapters.rs:527:                    signature: Some(format!("Link: {} -> {}", link_text, link_url)),
src/parser/adapters.rs:538:                    name: format!("image: {}", alt_text),
src/parser/adapters.rs:553:                    signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
src/parser/adapters.rs:564:                    name: format!("reference: {}", ref_name),
src/parser/adapters.rs:579:                    signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
src/parser/adapters.rs:589:                    name: format!("inline_code: {}", code),
src/parser/adapters.rs:604:                    signature: Some(format!("Inline code: {}", code)),
src/services/analyzer/events.rs:257:        let webhook_url = format!("{}/webhook", server.url());
src/services/analyzer/events.rs:286:        let webhook_url = format!("{}/webhook", server.url());
src/services/analyzer/progress.rs:106:            message: file_count.map(|count| format!("Found {count} source files")),
src/services/analyzer/progress.rs:159:            message: pattern_count.map(|count| format!("Found {count} code patterns")),
src/services/analyzer/progress.rs:176:            message: embedding_count.map(|count| format!("Generated {count} embeddings")),
src/services/analyzer/progress.rs:202:            message: Some(format!("Error: {error}")),
src/bin/test_parsers.rs:125:        let file_path = format!("test.{}", match lang {
src/api/errors.rs:115:            error_id: format!("error_{}", Uuid::new_v4().to_string().replace("-", "")[..16].to_string()),
src/api/errors.rs:277:                    ApiError::BadRequest(format!("Unsupported language: {language}")),
src/api/errors.rs:279:                    ApiError::BadRequest(format!("Failed to parse {file}: {reason}")),
src/api/errors.rs:281:                    ApiError::BadRequest(format!("Syntax error in {file} at line {line}: {details}")),
src/api/errors.rs:283:                    ApiError::InternalError(format!("Parser error: {msg}")),
src/api/errors.rs:285:                    ApiError::InternalError(format!("Regex compilation failed for '{pattern}': {reason}")),
src/api/errors.rs:287:                    ApiError::BadRequest(format!("File too large: {} bytes (limit: {} bytes)", size, limit)),
src/api/errors.rs:289:                    ApiError::ServiceUnavailable(format!("Parse timeout after {} seconds", timeout_seconds)),
src/api/errors.rs:291:                    ApiError::BadRequest(format!("Invalid encoding: {}", msg)),
src/api/errors.rs:293:                    ApiError::BadRequest(format!("Dependency parsing error: {}", msg)),
src/api/errors.rs:295:                    ApiError::InternalError(format!("AST traversal error: {}", msg)),
src/api/errors.rs:297:                    ApiError::ServiceUnavailable(format!("Parser pool error: {}", msg)),
src/api/errors.rs:302:                    ApiError::ServiceUnavailable(format!("Database connection failed: {}", msg)),
src/api/errors.rs:304:                    ApiError::ServiceUnavailable(format!("Query failed: {} - {}", query, reason)),
src/api/errors.rs:306:                    ApiError::ServiceUnavailable(format!("Transaction failed: {}", msg)),
src/api/errors.rs:308:                    ApiError::ServiceUnavailable(format!("Cache error: {}", msg)),
src/api/errors.rs:310:                    ApiError::InternalError(format!("Serialization error: {}", msg)),
src/api/errors.rs:312:                    ApiError::InternalError(format!("Deserialization error: {}", msg)),
src/api/errors.rs:316:                    ApiError::Conflict(format!("Constraint violation: {}", msg)),
src/api/errors.rs:320:                    ApiError::ServiceUnavailable(format!("Storage quota exceeded: {} / {}", current, limit)),
src/services/security/vulnerability/ml_classifier.rs:375:                       description: format!("{}: Potential SQL injection vulnerability", description),
src/services/security/vulnerability/ml_classifier.rs:411:                       description: format!("{}: Potential XSS vulnerability", description),
src/services/security/vulnerability/ml_classifier.rs:447:                       description: format!("{}: Potential command injection", description),
src/services/security/vulnerability/ml_classifier.rs:476:                       description: format!("{}: Potential path traversal", description),
src/services/security/vulnerability/ml_classifier.rs:520:                       description: format!("{}: Insecure deserialization", description),
src/services/security/vulnerability/ml_classifier.rs:557:                       description: format!("{}: Weak cryptography", description),
src/services/security/vulnerability/ml_classifier.rs:593:                       description: format!("{}: Authentication issue", description),
src/services/security/vulnerability/ml_classifier.rs:629:                       description: format!("{}: Security misconfiguration", description),
src/services/security/vulnerability/ml_classifier.rs:691:                       description: format!("{}: Potential race condition", description),
src/services/security/vulnerability/ml_classifier.rs:729:                       description: format!("{}: Buffer overflow risk", description),
src/storage/cache.rs:366:            let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
src/parser/validation_demo.rs:110:            _ => format!("test.{}", lang),
src/parser/mod.rs:98:                message: format!("Failed to read file metadata: {e}"),
src/parser/mod.rs:153:                    message: format!("Failed to detect language: {e}"),
src/parser/mod.rs:159:                    message: format!("Could not detect language for file: {file_name}"),
src/parser/mod.rs:177:                message: format!("Failed to get parser from pool: {e}"),
src/parser/mod.rs:230:                message: format!("Failed to read file: {e}"),
src/parser/mod.rs:268:                message: format!("No custom parser for language: {language}"),
src/parser/mod.rs:459:                message: format!("Language not supported: {language_name}"),
src/parser/mod.rs:469:                    message: format!("Failed to create parser pool: {e}"),
src/parser/mod.rs:496:                    message: format!("Failed to preload parsers: {e}"),
src/services/analyzer/mod.rs:405:                        format!("Failed to generate embeddings: {}", e),
src/services/analyzer/mod.rs:508:                .map(|p| format!("{:?}", p.pattern_type))
src/services/analyzer/mod.rs:514:                    .filter(|p| format!("{:?}", p.pattern_type) == pattern_type)
src/services/analyzer/file_processor.rs:148:                                stage: format!("Parsed {completed}/{total_files} files ({success_rate:.1}% success)"),
src/services/analyzer/file_processor.rs:149:                                message: Some(format!("Concurrent processing: {max_concurrent_files} active")),
src/services/analyzer/file_processor.rs:172:                            message: format!("Task panicked: {e}"),
src/services/analyzer/results.rs:83:                        format!("Failed to parse {}: {}", e.file_path, e.message),
src/services/analyzer/results.rs:187:            .map(|p| format!("{:?}", p.pattern_type))
src/services/analyzer/results.rs:224:            let severity_str = format!("{:?}", warning.severity);
src/services/analyzer/results.rs:373:            results.push(Ok(create_test_file_analysis(&format!("file{}.rs", i), 1000, 5)));
src/services/analyzer/results.rs:377:                file_path: format!("error{}.rs", i),
src/api/middleware/auth_layer.rs:475:        .map_err(|e| format!("Failed to get Spanner connection from pool: {:?}", e))?;
src/api/middleware/auth_layer.rs:497:        .map_err(|e| format!("Failed to create read transaction: {}", e))?;
src/api/middleware/auth_layer.rs:501:        .map_err(|e| format!("Failed to query API keys: {}", e))?;
src/api/middleware/auth_layer.rs:508:        .map_err(|e| format!("Failed to read row: {}", e))?
src/api/middleware/auth_layer.rs:512:            .map_err(|e| format!("Failed to read key_hash: {}", e))?;
src/api/middleware/auth_layer.rs:515:            .map_err(|e| format!("Failed to read salt: {}", e))?;
src/api/middleware/auth_layer.rs:524:                        .map_err(|e| format!("Invalid expiration time format: {}", e))?;
src/api/middleware/auth_layer.rs:532:                    .map_err(|e| format!("Failed to read user_id: {}", e))?;
src/api/middleware/auth_layer.rs:535:                    .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
src/api/middleware/auth_layer.rs:567:        .map_err(|e| format!("Failed to decode salt: {}", e))?;
src/api/middleware/auth_layer.rs:589:    let api_key = format!("ak_{}", general_purpose::STANDARD.encode(key_bytes));
src/api/middleware/auth_layer.rs:622:    let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {}", e))?;
src/api/middleware/auth_layer.rs:631:            .ok_or_else(|| format!("Unknown key ID: {}", kid))?
src/api/middleware/auth_layer.rs:667:            _ => format!("Token validation failed: {}", e),
src/api/middleware/auth_layer.rs:707:        .map_err(|e| format!("System time error: {}", e))?
src/api/middleware/auth_layer.rs:755:                .map_err(|e| format!("Failed to get spanner connection: {:?}", e))?;
src/api/middleware/auth_layer.rs:760:                .map_err(|e| format!("Failed to create read transaction: {}", e))?;
src/api/middleware/auth_layer.rs:764:                .map_err(|e| format!("Failed to query user: {}", e))?;
src/api/middleware/auth_layer.rs:768:                .map_err(|e| format!("Failed to read row: {}", e))?
src/api/middleware/auth_layer.rs:778:            .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
src/api/middleware/auth_layer.rs:849:    format!("{:x}", result)
src/api/middleware/auth_layer.rs:953:        operation: Some(format!("{} {}", request.method(), request.uri().path())),
src/api/middleware/auth_layer.rs:1065:        operation: Some(format!("{} {}", request.method(), request.uri().path())),
src/api/middleware/auth_layer.rs:1091:        description: format!("Wait {} seconds before making another request", retry_after),
src/api/middleware/auth_layer.rs:1139:    let key = format!("rate_limit:{}", user_id);
src/services/security/types.rs:65:            format!("Failed to compile pattern '{}': {}", name, e)
src/audit/mod.rs:256:                format!("{:?}", e)
src/services/security/secrets/detector.rs:127:            format!("{}***", &secret[..2])
src/services/security/secrets/detector.rs:129:            format!("{}...{}", &secret[..3], &secret[len-3..])
src/services/ai_pattern_detector.rs:287:            prompt.push_str(&format!("File: {}\n", analysis.path));
src/services/ai_pattern_detector.rs:288:            prompt.push_str(&format!("Language: {}\n", analysis.language));
src/services/ai_pattern_detector.rs:289:            prompt.push_str(&format!("Lines of code: {}\n", analysis.metrics.lines_of_code));
src/services/ai_pattern_detector.rs:290:            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));
src/services/ai_pattern_detector.rs:295:                    prompt.push_str(&format!("{} ", symbol.name));
src/services/ai_pattern_detector.rs:302:                prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
src/services/ai_pattern_detector.rs:432:                description.push_str(&format!("\n\nSuggestions:\n{}", ai_pattern.suggestions.join("\n")));
src/services/ai_pattern_detector.rs:436:                pattern_id: format!("ai_pattern_{}", Uuid::new_v4()),
src/services/repository_insights.rs:619:        prompt.push_str(&format!("Files analyzed: {}\n", analyses.len()));
src/services/repository_insights.rs:620:        prompt.push_str(&format!("Total lines: {}\n", repository_metrics.total_lines));
src/services/repository_insights.rs:621:        prompt.push_str(&format!("Average complexity: {:.2}\n", repository_metrics.average_complexity.unwrap_or(0.0)));
src/services/repository_insights.rs:622:        prompt.push_str(&format!("Maintainability score: {:.2}\n", repository_metrics.maintainability_score.unwrap_or(0.0)));
src/services/repository_insights.rs:627:            prompt.push_str(&format!("- {}: {:.1}% ({} files, {} lines)\n", 
src/services/repository_insights.rs:633:            prompt.push_str(&format!("\nDetected patterns: {}\n", patterns.len()));
src/services/repository_insights.rs:635:                prompt.push_str(&format!("- {:?}: {}\n", pattern.pattern_type, 
src/services/repository_insights.rs:643:            prompt.push_str(&format!("Overall score: {:.1}\n", quality.overall_score));
src/services/repository_insights.rs:644:            prompt.push_str(&format!("Maintainability: {:.1}\n", quality.maintainability_score));
src/services/repository_insights.rs:645:            prompt.push_str(&format!("Security: {:.1}\n", quality.security_score));
src/services/repository_insights.rs:646:            prompt.push_str(&format!("Performance: {:.1}\n", quality.performance_score));
src/services/repository_insights.rs:652:            prompt.push_str(&format!("\nFile: {}\n", analysis.path));
src/services/repository_insights.rs:653:            prompt.push_str(&format!("Language: {}\n", analysis.language));
src/services/repository_insights.rs:654:            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));
src/services/repository_insights.rs:658:                prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
src/services/repository_insights.rs:775:            overview: format!("Repository with {} files across {} languages", 
