./src/services/code_quality_assessor.rs:                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./src/services/analyzer/storage.rs:                            pattern_statement.add_param("line_start", &(pattern.location.range.start.line as i64));
./src/services/analyzer/storage.rs:                            pattern_statement.add_param("line_end", &(pattern.location.range.end.line as i64));
./src/services/analyzer/performance.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./src/services/analyzer/performance.rs:        let adaptive_batch_size = ((base_batch_size as f64 * load_factor) as usize)
./src/services/analyzer/repository.rs:                        duration_seconds: Some((Utc::now() - config.start_time).num_seconds() as u64),
./src/services/analyzer/progress.rs:        let progress = 35.0 + (files_processed as f64 / total_files as f64) * 35.0;
./src/services/analyzer/mod.rs:            duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
./src/services/analyzer/mod.rs:            success_rate: (successful_analyses.len() as f64 / files.len() as f64) * 100.0,
./src/services/analyzer/results.rs:                        e.position.map(|p| p.line as u32),
./src/services/analyzer/results.rs:            let failure_rate = (failed_files.len() as f64 / total_files as f64) * 100.0;
./src/services/analyzer/results.rs:                .map(|m| m.memory_peak_mb as f64),
./src/services/intelligent_documentation.rs:                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./src/services/semantic_search.rs:            size_bytes: Some(query.len() as u64),
./src/services/semantic_search.rs:            ((metrics.average_results_per_search * (total_searches - 1.0)) + results_count as f64) / total_searches;
./src/services/analyzer/streaming_processor.rs:                        let progress = 35.0 + (processed as f64 / total_files as f64) * 35.0;
./src/services/analyzer/streaming_processor.rs:                            ((processed - errors) as f64 / processed as f64) * 100.0
./src/services/analyzer/streaming_processor.rs:            ((total_processed - total_errors) as f64 / total_processed as f64) * 100.0
./src/services/analyzer/streaming_processor.rs:            size_bytes: Some(total_chars as u64),
./src/services/analyzer/streaming_processor.rs:                total_lines: Some(line_count as u32),
./src/services/analyzer/file_processor.rs:                        let progress = 35.0 + (completed as f64 / total_files as f64) * 35.0;
./src/services/analyzer/file_processor.rs:                            ((completed - errors) as f64 / completed as f64) * 100.0
./src/services/analyzer/file_processor.rs:            ((total_processed - total_errors) as f64 / total_processed as f64) * 100.0
./src/services/analyzer/file_processor.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./src/services/analyzer/file_processor.rs:        let adaptive_batch_size = ((base_batch_size as f64 * load_factor) as usize)
./src/services/embeddings_enhancement.rs:                    self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./src/services/embeddings_enhancement.rs:            vector[i] = (((hash_value >> (i % 64)) & 1) as f32) * 2.0 - 1.0;
./src/metrics/granular.rs:            entry.avg_parse_time_ms = (total_parse_time_ms + parse_time.as_millis() as f64) / entry.files_parsed as f64;
./src/metrics/granular.rs:        ACTIVE_TASKS.set(active_analyses as f64);
./src/metrics/granular.rs:        MEMORY_ALLOCATED_BYTES.set(allocated_bytes as f64);
./src/parser/parser_pool.rs:            utilization: if total > 0 { (in_use as f64 / total as f64) * 100.0 } else { 0.0 },
./src/parser/parser_pool.rs:            let needed = ((stats.in_use_parsers as f64 / target_utilization * 100.0) as usize)
./src/parser/parser_pool.rs:        let target_total = ((expected_concurrent_requests as f64 * growth_multiplier) as usize)
./src/metrics/mod.rs:                        ((total - idle) as f64 / total as f64) * 100.0
./src/metrics/mod.rs:            (stats.memory_used as f64 / stats.memory_total as f64) * 100.0
./src/metrics/mod.rs:            Some(total_complexity as f64 / total_files as f64)
./src/metrics/mod.rs:            Some(maintainability_sum / total_files as f64)
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/code_quality_assessor.rs:                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/storage.rs:                            pattern_statement.add_param("line_start", &(pattern.location.range.start.line as i64));
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/storage.rs:                            pattern_statement.add_param("line_end", &(pattern.location.range.end.line as i64));
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/performance.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/performance.rs:        let adaptive_batch_size = ((base_batch_size as f64 * load_factor) as usize)
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/repository.rs:                        duration_seconds: Some((Utc::now() - config.start_time).num_seconds() as u64),
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/progress.rs:        let progress = 35.0 + (files_processed as f64 / total_files as f64) * 35.0;
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/mod.rs:            duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/mod.rs:            success_rate: (successful_analyses.len() as f64 / files.len() as f64) * 100.0,
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/results.rs:                        e.position.map(|p| p.line as u32),
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/results.rs:            let failure_rate = (failed_files.len() as f64 / total_files as f64) * 100.0;
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/analyzer/results.rs:                .map(|m| m.memory_peak_mb as f64),
./evidence/agent-03/potential-unnecessary-casts.txt:./src/services/intelligent_documentation.rs:                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./evidence/agent-03/clippy-warnings-detailed.txt:86 |                         e.position.map(|p| p.line as u32),
./evidence/agent-03/clippy-warnings-detailed.txt:369 |                 total_lines: Some(line_count as u32),
./evidence/agent-03/clippy-warnings-detailed.txt:628 |         statement.add_param("limit", &(per_page as i64));
./evidence/agent-03/clippy-warnings-detailed.txt:629 |         statement.add_param("offset", &(offset as i64));
./src/storage/redis_client.rs:            let _: () = conn.expire(&rate_limit_key, window_seconds as i64).await?;
./src/storage/redis_client.rs:                (*score as u64) + window_seconds
./src/storage/redis_client.rs:            let _: () = conn.expire(&burst_key, burst_window as i64).await?;
./src/services/language_detector.rs:                (lines as f64 / total_lines as f64) * 100.0
./src/api/handlers/websocket.rs:                                .map(|m| m.files_analyzed as usize),
./src/api/handlers/websocket.rs:                                                .map(|m| m.files_analyzed as usize),
./src/api/handlers/websocket.rs:                                                .map(|m| m.files_analyzed as usize),
./evidence/agent-03/manual-clamp-instances.txt:./src/services/analyzer/performance.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/manual-clamp-instances.txt:./evidence/agent-03/clippy-before.txt:274 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/manual-clamp-instances.txt:./evidence/agent-03/clippy-before.txt:189 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/manual-clamp-instances.txt:./src/services/analyzer/file_processor.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./src/storage/spanner.rs:                stats.avg_connection_time_ms = (stats.avg_connection_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/storage/spanner.rs:                stats.avg_connection_time_ms = (stats.avg_connection_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/storage/spanner.rs:                stats.avg_connection_time_ms = (stats.avg_connection_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/storage/spanner.rs:        stats.pool_utilization = (stats.active_connections as f64 / self.config.max_connections as f64) * 100.0;
./src/storage/spanner.rs:                    stats_guard.failed_operations += (batch.len() as u64) - success_count;
./src/storage/spanner.rs:                    stats_guard.avg_batch_size = (stats_guard.avg_batch_size + batch.len() as f64) / 2.0;
./src/storage/spanner.rs:                    stats_guard.avg_processing_time_ms = (stats_guard.avg_processing_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/storage/spanner.rs:        let file_count = Arc::new(analysis.file_count as i64);
./src/storage/spanner.rs:                        statement.add_param("repository_size_bytes", &(size as i64));
./src/storage/spanner.rs:                        statement.add_param("clone_time_ms", &(time as i64));
./src/storage/spanner.rs:                        statement.add_param("duration_seconds", &(duration as f64));
./src/storage/spanner.rs:        statement.add_param("limit", &(per_page as i64));
./src/storage/spanner.rs:        statement.add_param("offset", &(offset as i64));
./src/storage/spanner.rs:        statement.add_param("size_bytes", &(file_analysis.size_bytes.unwrap_or(0) as i64));
./src/storage/spanner.rs:        statement.add_param("lines_of_code", &(file_analysis.metrics.lines_of_code as i64));
./src/storage/spanner.rs:        statement.add_param("total_lines", &(file_analysis.metrics.total_lines.unwrap_or(0) as i64));
./src/storage/spanner.rs:        statement.add_param("complexity_score", &(file_analysis.metrics.complexity as f64));
./src/storage/spanner.rs:            size_bytes: size_bytes.map(|s| s as u64),
./src/storage/spanner.rs:        let duration_seconds = duration_seconds.map(|d| d as u64);
./src/storage/spanner.rs:            repository_size_bytes: repository_size_bytes.map(|v| v as u64),
./src/storage/spanner.rs:            clone_time_ms: clone_time_ms.map(|v| v as u64),
./evidence/agent-03/clippy-before.txt:274 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/clippy-before.txt:    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace with clamp: `(50.0 / current_load as f64).clamp(0.5, 2.0)`
./evidence/agent-03/clippy-before.txt:189 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/clippy-before.txt:    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace with clamp: `(50.0 / current_load as f64).clamp(0.5, 2.0)`
./evidence/agent-03/clippy-before.txt:86 |                         e.position.map(|p| p.line as u32),
./evidence/agent-03/clippy-before.txt:369 |                 total_lines: Some(line_count as u32),
./evidence/agent-03/clippy-before.txt:628 |         statement.add_param("limit", &(per_page as i64));
./evidence/agent-03/clippy-before.txt:629 |         statement.add_param("offset", &(offset as i64));
./src/services/security/vulnerability/detector.rs:                        line_start: Some(match_info.line_number as i64),
./src/services/security/vulnerability/detector.rs:                        line_end: Some(match_info.line_number as i64),
./src/services/security/vulnerability/detector.rs:                        line_start: Some(node.range.start.line as i64),
./src/services/security/vulnerability/detector.rs:                        line_end: Some(node.range.end.line as i64),
./Dockerfile:# Set user (distroless already runs as nonroot)
./tests/comprehensive_test_framework.rs:                metrics.insert("concurrent_analyses".to_string(), successful_count as f64);
./tests/comprehensive_test_framework.rs:            (self.passed_tests as f64 / self.total_tests as f64) * 100.0
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_start: Some((line_num + 1) as i64),
./src/services/security/vulnerability/ml_classifier.rs:                       line_end: Some((line_num + 1) as i64),
./src/services/security/mod.rs:            scan_duration_ms: Some(start_time.elapsed().as_millis() as i64),
./src/services/security/mod.rs:            total_files_scanned: Some(file_analyses.len() as i64),
./src/services/security/mod.rs:            total_dependencies_scanned: Some(self.count_dependencies(file_analyses) as i64),
./tests/security_parsers_test.rs:        size_bytes: Some(content.len() as u64),
./tests/security_parsers_test.rs:            total_lines: Some(content.lines().count() as u32),
./tests/comprehensive_test_framework/e2e_testing.rs:        let workflow_success_rate = (successful_workflows as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/e2e_testing.rs:            .map(|r| r.completion_time.as_secs() as f64)
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("total_workflow_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("successful_workflows".to_string(), successful_workflows as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:            metrics.insert(format!("scenario_{}_completion_time_seconds", i), result.completion_time.as_secs() as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:            metrics.insert(format!("scenario_{}_phases_completed", i), result.phases_completed.len() as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        let integration_success_rate = (successful_integrations as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("total_integration_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("successful_integrations".to_string(), successful_integrations as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:            metrics.insert(format!("scenario_{}_response_time_ms", i), result.response_time.as_millis() as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        let scenario_success_rate = (successful_scenarios as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("total_user_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        metrics.insert("successful_scenarios".to_string(), successful_scenarios as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:            metrics.insert(format!("scenario_{}_steps_completed", i), result.steps_completed as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:            metrics.insert(format!("scenario_{}_completion_time_seconds", i), result.completion_time.as_secs() as f64);
./tests/comprehensive_test_framework/e2e_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./src/services/security/compliance/checker.rs:                            line_number: Some(line_number as i64),
./tests/comprehensive_test_framework/ci_cd_testing.rs:        let build_success_rate = (successful_builds as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/ci_cd_testing.rs:            .map(|r| r.build_time.as_secs() as f64)
./tests/comprehensive_test_framework/ci_cd_testing.rs:        metrics.insert("total_build_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:        metrics.insert("successful_builds".to_string(), successful_builds as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:            metrics.insert(format!("build_{}_time_seconds", i), result.build_time.as_secs() as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:        let deployment_success_rate = (successful_deployments as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/ci_cd_testing.rs:            .map(|r| r.deployment_time.as_secs() as f64)
./tests/comprehensive_test_framework/ci_cd_testing.rs:        metrics.insert("total_deployment_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:        metrics.insert("successful_deployments".to_string(), successful_deployments as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:            metrics.insert(format!("deployment_{}_time_seconds", i), result.deployment_time.as_secs() as f64);
./tests/comprehensive_test_framework/ci_cd_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./src/storage/cache.rs:                stats.avg_access_time_ms = (stats.avg_access_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/storage/cache.rs:                    stats.avg_access_time_ms = (stats.avg_access_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
./src/api/rate_limit_extractor.rs:        error_response.retry_after_seconds = Some(reset_time as u32);
./tests/comprehensive_test_framework/performance_testing.rs:        let success_rate = (successful_count as f64 / self.config.max_concurrent_analyses as f64) * 100.0;
./tests/comprehensive_test_framework/performance_testing.rs:            .map(|r| r.duration.as_millis() as f64)
./tests/comprehensive_test_framework/performance_testing.rs:            .map(|r| r.duration.as_millis() as f64)
./tests/comprehensive_test_framework/performance_testing.rs:            .map(|r| r.duration.as_millis() as f64)
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("total_requests".to_string(), self.config.max_concurrent_analyses as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("successful_requests".to_string(), successful_count as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("requests_sent".to_string(), request_count as f64);
./tests/comprehensive_test_framework/performance_testing.rs:                response_times.push(duration.as_millis() as f64);
./tests/comprehensive_test_framework/performance_testing.rs:            let p95 = sorted_times[(0.95 * sorted_times.len() as f64) as usize];
./tests/comprehensive_test_framework/performance_testing.rs:            let p99 = sorted_times[(0.99 * sorted_times.len() as f64) as usize];
./tests/comprehensive_test_framework/performance_testing.rs:        let success_rate = (successful_requests as f64 / request_count as f64) * 100.0;
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("total_requests".to_string(), request_count as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("successful_requests".to_string(), successful_requests as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("failed_requests".to_string(), failed_requests as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        metrics.insert("requests_sent".to_string(), request_count as f64);
./tests/comprehensive_test_framework/performance_testing.rs:        let success_rate = (successful_count as f64 / concurrency as f64) * 100.0;
./tests/comprehensive_test_framework/performance_testing.rs:            .map(|(_, duration)| duration.as_millis() as f64)
./tests/comprehensive_test_framework/performance_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./src/api/mod.rs:            update_system_metrics(memory_bytes, cpu_usage as f64);
./src/api/metrics_handler.rs:        (total_successes as f64 / total_operations as f64) * 100.0
./src/api/metrics_handler.rs:        (total_errors as f64 / total_operations as f64) * 100.0
./src/api/metrics_handler.rs:        (total_errors as f64 / total_operations as f64) * 100.0
./src/api/metrics_handler.rs:        (errors.auto_recovered as f64 / total_errors as f64) * 100.0
./src/parser/mod.rs:                size_bytes: Some(content.len() as u64),
./src/parser/mod.rs:            size_bytes: Some(content.len() as u64),
./src/parser/mod.rs:            size_bytes: Some(content.len() as u64),
./src/parser/mod.rs:            size_bytes: Some(content.len() as u64),
./src/parser/mod.rs:            size_bytes: Some(content.len() as u64),
./tests/comprehensive_test_framework/ai_feature_testing.rs:        let integration_rate = (successful_integrations as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("total_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("successful_integrations".to_string(), successful_integrations as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:            metrics.insert(format!("scenario_{}_response_time_ms", i), result.response_time.as_millis() as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        let circuit_breaker_effectiveness = (successful_tests as f64 / total_tests as f64) * 100.0;
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("total_circuit_breaker_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("successful_circuit_breaker_tests".to_string(), successful_tests as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:            metrics.insert(format!("test_{}_duration_ms", i), test.duration.as_millis() as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        let reliability_rate = (successful_tests as f64 / total_tests as f64) * 100.0;
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("total_reliability_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("successful_reliability_tests".to_string(), successful_tests as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:            metrics.insert(format!("test_{}_duration_ms", i), result.duration.as_millis() as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        let fallback_success_rate = (successful_fallbacks as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("total_fallback_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        metrics.insert("successful_fallbacks".to_string(), successful_fallbacks as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:            metrics.insert(format!("scenario_{}_duration_ms", i), result.duration.as_millis() as f64);
./tests/comprehensive_test_framework/ai_feature_testing.rs:        let success_rate = (successful_requests as f64 / total_requests as f64) * 100.0;
./tests/comprehensive_test_framework/ai_feature_testing.rs:            .map(|r| r.features_working as f64)
./tests/comprehensive_test_framework/ai_feature_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./tests/ai_integration_tests.rs:            size_bytes: Some(content.len() as u64),
./tests/ai_integration_tests.rs:                total_lines: Some(content.lines().count() as u32),
./tests/ai_integration_tests.rs:            size_bytes: Some(content.len() as u64),
./tests/ai_integration_tests.rs:                total_lines: Some(content.lines().count() as u32),
./tests/comprehensive_test_framework/security_testing.rs:        let detection_rate = (detected_vulnerabilities as f64 / total_vulnerabilities as f64) * 100.0;
./tests/comprehensive_test_framework/security_testing.rs:        let false_positive_rate = (false_positives as f64 / total_vulnerabilities as f64) * 100.0;
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("total_vulnerabilities".to_string(), total_vulnerabilities as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("detected_vulnerabilities".to_string(), detected_vulnerabilities as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("false_positives".to_string(), false_positives as f64);
./tests/comprehensive_test_framework/security_testing.rs:            (detected_secrets as f64 / total_secrets as f64) * 100.0
./tests/comprehensive_test_framework/security_testing.rs:        let false_positive_rate = (false_positives as f64 / secret_test_cases.len() as f64) * 100.0;
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("total_secrets".to_string(), total_secrets as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("detected_secrets".to_string(), detected_secrets as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("false_positives".to_string(), false_positives as f64);
./tests/comprehensive_test_framework/security_testing.rs:        let false_positive_rate = (false_positives as f64 / total_samples as f64) * 100.0;
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("total_samples".to_string(), total_samples as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("false_positives".to_string(), false_positives as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("frameworks_tested".to_string(), compliance_frameworks.len() as f64);
./tests/comprehensive_test_framework/security_testing.rs:            (detected_threats as f64 / total_threats as f64) * 100.0
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("scenarios_tested".to_string(), threat_scenarios.len() as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("total_threats".to_string(), total_threats as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("detected_threats".to_string(), detected_threats as f64);
./tests/comprehensive_test_framework/security_testing.rs:            (intelligence_features_working as f64 / intelligence_features_tested as f64) * 100.0
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("features_tested".to_string(), intelligence_features_tested as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("features_working".to_string(), intelligence_features_working as f64);
./tests/comprehensive_test_framework/security_testing.rs:            (total_detected_vulnerabilities as f64 / total_expected_vulnerabilities as f64) * 100.0
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("repos_tested".to_string(), vulnerable_repos.len() as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("expected_vulnerabilities".to_string(), total_expected_vulnerabilities as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("detected_vulnerabilities".to_string(), total_detected_vulnerabilities as f64);
./tests/comprehensive_test_framework/security_testing.rs:            .map(|(_, duration)| duration.as_millis() as f64)
./tests/comprehensive_test_framework/security_testing.rs:        let success_rate = (successful_requests as f64 / concurrent_requests as f64) * 100.0;
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("concurrent_requests".to_string(), concurrent_requests as f64);
./tests/comprehensive_test_framework/security_testing.rs:        metrics.insert("successful_requests".to_string(), successful_requests as f64);
./tests/comprehensive_test_framework/security_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./tests/comprehensive_test_framework/language_validation.rs:        let support_rate = (supported_count as f64 / total_languages as f64) * 100.0;
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("total_languages_tested".to_string(), total_languages as f64);
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("supported_languages".to_string(), supported_count as f64);
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("unsupported_languages".to_string(), unsupported_languages.len() as f64);
./tests/comprehensive_test_framework/language_validation.rs:            metrics.insert(format!("{}_parse_time_ms", lang), result.parse_time.as_millis() as f64);
./tests/comprehensive_test_framework/language_validation.rs:        let emerging_support_rate = (supported_count as f64 / total_emerging as f64) * 100.0;
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("total_emerging_languages".to_string(), total_emerging as f64);
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("supported_emerging_languages".to_string(), supported_count as f64);
./tests/comprehensive_test_framework/language_validation.rs:            metrics.insert(format!("{}_parse_time_ms", lang), result.parse_time.as_millis() as f64);
./tests/comprehensive_test_framework/language_validation.rs:        let detection_accuracy = (correct_detections as f64 / total_tests as f64) * 100.0;
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("total_detection_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("correct_detections".to_string(), correct_detections as f64);
./tests/comprehensive_test_framework/language_validation.rs:        metrics.insert("total_ast_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/language_validation.rs:            metrics.insert(format!("test_{}_nodes_detected", i), result.nodes_detected as f64);
./tests/comprehensive_test_framework/language_validation.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./tests/comprehensive_test_framework/database_testing.rs:        let migration_success_rate = (successful_migrations as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("total_migration_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("successful_migrations".to_string(), successful_migrations as f64);
./tests/comprehensive_test_framework/database_testing.rs:            metrics.insert(format!("migration_{}_duration_ms", i), result.duration.as_millis() as f64);
./tests/comprehensive_test_framework/database_testing.rs:        let rollback_success_rate = (successful_rollbacks as f64 / total_scenarios as f64) * 100.0;
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("total_rollback_scenarios".to_string(), total_scenarios as f64);
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("successful_rollbacks".to_string(), successful_rollbacks as f64);
./tests/comprehensive_test_framework/database_testing.rs:            metrics.insert(format!("rollback_{}_duration_ms", i), result.duration.as_millis() as f64);
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("total_integrity_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("total_violations".to_string(), total_violations as f64);
./tests/comprehensive_test_framework/database_testing.rs:            metrics.insert(format!("test_{}_violations", i), result.violations_found as f64);
./tests/comprehensive_test_framework/database_testing.rs:            metrics.insert(format!("test_{}_duration_ms", i), result.duration.as_millis() as f64);
./tests/comprehensive_test_framework/database_testing.rs:        let performance_score = (passed_tests as f64 / total_tests as f64) * 100.0;
./tests/comprehensive_test_framework/database_testing.rs:            .map(|r| r.actual_time.as_millis() as f64)
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("total_performance_tests".to_string(), total_tests as f64);
./tests/comprehensive_test_framework/database_testing.rs:        metrics.insert("passed_performance_tests".to_string(), passed_tests as f64);
./tests/comprehensive_test_framework/database_testing.rs:            metrics.insert(format!("test_{}_time_ms", i), result.actual_time.as_millis() as f64);
./tests/comprehensive_test_framework/database_testing.rs:        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
./src/backpressure/mod.rs:            let throttle_duration = Duration::from_millis((load_factor * 1000.0) as u64);
./tests/common/chaos.rs:            .map(|r| r.execution_time.as_millis() as f64)
./tests/common/chaos.rs:            (self.passed_scenarios as f64 / self.total_scenarios as f64) * 100.0);
./src/services/ai_pattern_detector.rs:                    self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./src/services/repository_insights.rs:                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
./tests/common/mock_factory.rs:                let seed = hash.wrapping_add(i as u64);
./src/parser/language_metrics.rs:            score -= (complexity as f64 - factors.max_cyclomatic_complexity as f64)
./src/parser/language_metrics.rs:                score -= (avg_function_length - factors.max_function_length as f64)
./src/api/middleware/auth_layer.rs:        let ttl: i64 = conn.ttl(&key).await.unwrap_or(window_seconds as i64);
./src/api/middleware/auth_layer.rs:        let _: () = conn.expire(&key, window_seconds as i64).await?;
./src/services/security/secrets/detector.rs:                        line_number: Some(line_number as i64),
