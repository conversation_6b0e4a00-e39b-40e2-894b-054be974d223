warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:156:17
    |
156 | /                 format!(
157 | |                     "Peak memory usage was {} MB, approaching system limits",
158 | |                     memory_usage_mb
159 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:166:17
    |
166 | /                 format!(
167 | |                     "Peak memory usage was {} MB, consider monitoring memory usage",
168 | |                     memory_usage_mb
169 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: this `impl` can be derived
  --> src/services/analyzer/progress.rs:13:1
   |
13 | / impl Default for ProgressDetails {
14 | |     fn default() -> Self {
15 | |         Self {
16 | |             message: None,
...  |
21 | | }
   | |_^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
   = note: `#[warn(clippy::derivable_impls)]` on by default
help: replace the manual implementation with a derive attribute
   |
7  + #[derive(Default)]
8  ~ pub struct ProgressDetails {
   |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/progress.rs:125:20
    |
125 |               stage: format!(
    |  ____________________^
126 | |                 "Parsed {}/{} files ({:.1}% success)",
127 | |                 files_processed, total_files, success_rate
128 | |             ),
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/services/analyzer/repository.rs:89:21
   |
89 |                     format!("Failed to get remote commit hash: {}", e),
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
89 -                     format!("Failed to get remote commit hash: {}", e),
89 +                     format!("Failed to get remote commit hash: {e}"),
   |

warning: length comparison to zero
  --> src/services/analyzer/results.rs:99:12
   |
99 |         if failed_files.len() > 0 {
   |            ^^^^^^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!failed_files.is_empty()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero
   = note: `#[warn(clippy::len_zero)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/results.rs:104:21
    |
104 | /                     format!(
105 | |                         "High failure rate: {:.1}% of files failed to parse",
106 | |                         failure_rate
107 | |                     ),
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: using `clone` on type `AnalysisStatus` which implements the `Copy` trait
   --> src/services/analyzer/results.rs:197:21
    |
197 |             status: result.status.clone(),
    |                     ^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `result.status`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy
    = note: `#[warn(clippy::clone_on_copy)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/storage.rs:181:29
    |
181 |                 errors.push(format!("Spanner deletion failed: {}", e));
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
181 -                 errors.push(format!("Spanner deletion failed: {}", e));
181 +                 errors.push(format!("Spanner deletion failed: {e}"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/storage.rs:190:25
    |
190 |             errors.push(format!("Cloud storage deletion failed: {}", e));
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
190 -             errors.push(format!("Cloud storage deletion failed: {}", e));
190 +             errors.push(format!("Cloud storage deletion failed: {e}"));
    |

warning: you seem to use `.enumerate()` and immediately discard the index
  --> src/services/analyzer/streaming_processor.rs:71:36
   |
71 |         for (_batch_idx, batch) in files.chunks(batch_size).enumerate() {
   |                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unused_enumerate_index
   = note: `#[warn(clippy::unused_enumerate_index)]` on by default
help: remove the `.enumerate()` call
   |
71 -         for (_batch_idx, batch) in files.chunks(batch_size).enumerate() {
71 +         for batch in files.chunks(batch_size) {
   |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:223:26
    |
223 |                 message: format!("Failed to get file metadata: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
223 -                 message: format!("Failed to get file metadata: {}", e),
223 +                 message: format!("Failed to get file metadata: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:239:26
    |
239 |                 message: format!("Failed to open file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
239 -                 message: format!("Failed to open file: {}", e),
239 +                 message: format!("Failed to open file: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:252:26
    |
252 |                 message: format!("Failed to read file chunk: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
252 -                 message: format!("Failed to read file chunk: {}", e),
252 +                 message: format!("Failed to read file chunk: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:294:26
    |
294 |                 message: format!("Failed to open file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
294 -                 message: format!("Failed to open file: {}", e),
294 +                 message: format!("Failed to open file: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:365:28
    |
365 |                 text: Some(format!("Large file with {} lines", line_count)),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
365 -                 text: Some(format!("Large file with {} lines", line_count)),
365 +                 text: Some(format!("Large file with {line_count} lines")),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/mod.rs:404:25
    |
404 |                         format!("Failed to generate embeddings: {}", e),
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
404 -                         format!("Failed to generate embeddings: {}", e),
404 +                         format!("Failed to generate embeddings: {e}"),
    |

warning: you should consider adding a `Default` implementation for `LanguageDetector`
  --> src/services/language_detector.rs:10:5
   |
10 | /     pub fn new() -> Self {
11 | |         Self
12 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
   = note: `#[warn(clippy::new_without_default)]` on by default
help: try adding this
   |
9  + impl Default for LanguageDetector {
10 +     fn default() -> Self {
11 +         Self::new()
12 +     }
13 + }
   |

warning: iterating on a map's values
  --> src/services/language_detector.rs:20:34
   |
20 |         let total_lines: usize = languages.iter().map(|(_, stat)| stat.lines()).sum();
   |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `languages.values().map(|stat| stat.lines())`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#iter_kv_map
   = note: `#[warn(clippy::iter_kv_map)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings.rs:279:31
    |
279 |                     chunk_id: format!("chunk_{:016x}", i),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
279 -                     chunk_id: format!("chunk_{:016x}", i),
279 +                     chunk_id: format!("chunk_{i:016x}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings.rs:386:31
    |
386 |             content.push_str(&format!("\nCode snippet:\n{}", preview));
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
386 -             content.push_str(&format!("\nCode snippet:\n{}", preview));
386 +             content.push_str(&format!("\nCode snippet:\n{preview}"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:323:31
    |
323 |                     chunk_id: format!("chunk_{:016x}", i),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
323 -                     chunk_id: format!("chunk_{:016x}", i),
323 +                     chunk_id: format!("chunk_{i:016x}"),
    |

warning: the loop variable `i` is used to index `vector`
   --> src/services/embeddings_enhancement.rs:362:18
    |
362 |         for i in 0..EMBEDDING_DIMENSION {
    |                  ^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_range_loop
    = note: `#[warn(clippy::needless_range_loop)]` on by default
help: consider using an iterator and enumerate()
    |
362 -         for i in 0..EMBEDDING_DIMENSION {
362 +         for (i, <item>) in vector.iter_mut().enumerate().take(EMBEDDING_DIMENSION) {
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:367:23
    |
367 |             chunk_id: format!("fallback_chunk_{:016x}", idx),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
367 -             chunk_id: format!("fallback_chunk_{:016x}", idx),
367 +             chunk_id: format!("fallback_chunk_{idx:016x}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:442:31
    |
442 |             content.push_str(&format!("\nCode:\n{}", preview));
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -             content.push_str(&format!("\nCode:\n{}", preview));
442 +             content.push_str(&format!("\nCode:\n{preview}"));
    |

warning: you should consider adding a `Default` implementation for `PatternDetector`
  --> src/services/pattern_detector.rs:14:5
   |
14 | /     pub fn new() -> Self {
15 | |         Self {
16 | |             long_method_threshold: 50,
17 | |             complexity_threshold: 10,
18 | |         }
19 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
13 + impl Default for PatternDetector {
14 +     fn default() -> Self {
15 +         Self::new()
16 +     }
17 + }
   |

warning: this `map_or` can be simplified
   --> src/services/pattern_detector.rs:338:13
    |
338 | /             child.name.as_ref().map_or(false, |n| 
339 | |                 n.starts_with("create") || n.starts_with("build") || n.starts_with("make")
340 | |             )
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
    = note: `#[warn(clippy::unnecessary_map_or)]` on by default
help: use is_some_and instead
    |
338 -             child.name.as_ref().map_or(false, |n| 
338 +             child.name.as_ref().is_some_and(|n| 
    |

warning: parameter is only used in recursion
   --> src/services/pattern_detector.rs:371:30
    |
371 |     fn calculate_complexity(&self, node: &AstNode) -> usize {
    |                              ^^^^
    |
note: parameter used here
   --> src/services/pattern_detector.rs:394:27
    |
394 |             complexity += self.calculate_complexity(child);
    |                           ^^^^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion
    = note: `#[warn(clippy::only_used_in_recursion)]` on by default

warning: parameter is only used in recursion
   --> src/services/pattern_detector.rs:414:34
    |
414 |     fn has_string_concatenation(&self, node: &AstNode) -> bool {
    |                                  ^^^^
    |
note: parameter used here
   --> src/services/pattern_detector.rs:431:16
    |
431 |             if self.has_string_concatenation(child) {
    |                ^^^^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion

warning: calling `push_str()` using a single-character string literal
   --> src/services/ai_pattern_detector.rs:297:17
    |
297 |                 prompt.push_str("\n");
    |                 ^^^^^^^^^^^^^^^^^^^^^ help: consider using `push` with a character literal: `prompt.push('\n')`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_char_add_str
    = note: `#[warn(clippy::single_char_add_str)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/ai_pattern_detector.rs:302:34
    |
302 |                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
302 -                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
302 +                 prompt.push_str(&format!("Code:\n```\n{preview}\n```\n\n"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/code_quality_assessor.rs:412:30
    |
412 |             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
412 -             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
412 +             prompt.push_str(&format!("Technical debt: {technical_debt} minutes\n"));
    |

warning: calling `push_str()` using a single-character string literal
   --> src/services/code_quality_assessor.rs:429:17
    |
429 |                 prompt.push_str("\n");
    |                 ^^^^^^^^^^^^^^^^^^^^^ help: consider using `push` with a character literal: `prompt.push('\n')`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_char_add_str

warning: variables can be used directly in the `format!` string
   --> src/services/code_quality_assessor.rs:434:34
    |
434 |                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
434 -                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
434 +                 prompt.push_str(&format!("Code preview:\n```\n{preview}\n```\n\n"));
    |

warning: useless use of `format!`
   --> src/services/repository_insights.rs:642:30
    |
642 |             prompt.push_str(&format!("\nQuality Assessment:\n"));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using `.to_string()`: `"\nQuality Assessment:\n".to_string()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#useless_format
    = note: `#[warn(clippy::useless_format)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/repository_insights.rs:658:34
    |
658 |                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
658 -                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
658 +                 prompt.push_str(&format!("Code sample:\n```\n{preview}\n```\n"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:708:26
    |
708 |         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
708 -         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
708 +         prompt.push_str(&format!("Repository URL: {repository_url}\n"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:709:26
    |
709 |         prompt.push_str(&format!("Primary language: {}\n", primary_language));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
709 -         prompt.push_str(&format!("Primary language: {}\n", primary_language));
709 +         prompt.push_str(&format!("Primary language: {primary_language}\n"));
    |

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/intelligent_documentation.rs:844:25
    |
844 |         let repo_name = repository_url.split('/').last().unwrap_or("Repository");
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                   |
    |                                                   help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last
    = note: `#[warn(clippy::double_ended_iterator_last)]` on by default

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/intelligent_documentation.rs:855:23
    |
855 |                 name: analysis.path.split('/').last().unwrap_or(&analysis.path).to_string(),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                |
    |                                                help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:930:30
    |
930 |                 description: format!("{} repository written in {}", repo_name, primary_language),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
930 -                 description: format!("{} repository written in {}", repo_name, primary_language),
930 +                 description: format!("{repo_name} repository written in {primary_language}"),
    |

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/services/semantic_search.rs:226:37
    |
226 |             if !self.passes_filters(&query, &file_analysis) {
    |                                     ^^^^^^ help: change this to: `query`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow
    = note: `#[warn(clippy::needless_borrow)]` on by default

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/services/semantic_search.rs:236:17
    |
236 |                 &query,
    |                 ^^^^^^ help: change this to: `query`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/semantic_search.rs:368:38
    |
368 |                 let file_extension = analysis.path.split('.').last().unwrap_or("");
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                               |
    |                                                               help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: this `else { if .. }` block can be collapsed
   --> src/services/semantic_search.rs:405:79
    |
405 |               language_match: if query.language_filters.is_empty() { 1.0 } else {
    |  _______________________________________________________________________________^
406 | |                 if let Some(analysis) = file_analysis {
407 | |                     if query.language_filters.contains(&analysis.language) { 1.0 } else { 0.5 }
408 | |                 } else { 0.5 }
409 | |             },
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_else_if
    = note: `#[warn(clippy::collapsible_else_if)]` on by default
help: collapse nested if block
    |
405 ~             language_match: if query.language_filters.is_empty() { 1.0 } else if let Some(analysis) = file_analysis {
406 +                 if query.language_filters.contains(&analysis.language) { 1.0 } else { 0.5 }
407 ~             } else { 0.5 },
    |

warning: variables can be used directly in the `format!` string
   --> src/services/semantic_search.rs:535:9
    |
535 | ...   format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
    |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
535 -         format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
535 +         format!("This code is related to your query '{query}' based on content analysis. The code contains relevant functionality that matches your search criteria.")
    |

warning: variables can be used directly in the `format!` string
  --> src/services/security/types.rs:65:13
   |
65 |             format!("Failed to compile pattern '{}': {}", name, e)
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
65 -             format!("Failed to compile pattern '{}': {}", name, e)
65 +             format!("Failed to compile pattern '{name}': {e}")
   |

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/vulnerability/detector.rs:76:35
   |
76 |                         severity: pattern.severity.clone(),
   |                                   ^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `pattern.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/detector.rs:136:38
    |
136 |                         description: format!("Potentially dangerous function call: {}", name),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
136 -                         description: format!("Potentially dangerous function call: {}", name),
136 +                         description: format!("Potentially dangerous function call: {name}"),
    |

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/security/vulnerability/ml_classifier.rs:289:30
    |
289 |         let file_extension = file_analysis.path.split('.').last().unwrap_or("");
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                            |
    |                                                            help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:375:37
    |
375 |                        description: format!("{}: Potential SQL injection vulnerability", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
375 -                        description: format!("{}: Potential SQL injection vulnerability", description),
375 +                        description: format!("{description}: Potential SQL injection vulnerability"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:411:37
    |
411 |                        description: format!("{}: Potential XSS vulnerability", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
411 -                        description: format!("{}: Potential XSS vulnerability", description),
411 +                        description: format!("{description}: Potential XSS vulnerability"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:447:37
    |
447 |                        description: format!("{}: Potential command injection", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
447 -                        description: format!("{}: Potential command injection", description),
447 +                        description: format!("{description}: Potential command injection"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:476:37
    |
476 |                        description: format!("{}: Potential path traversal", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
476 -                        description: format!("{}: Potential path traversal", description),
476 +                        description: format!("{description}: Potential path traversal"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:520:37
    |
520 |                        description: format!("{}: Insecure deserialization", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
520 -                        description: format!("{}: Insecure deserialization", description),
520 +                        description: format!("{description}: Insecure deserialization"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:557:37
    |
557 |                        description: format!("{}: Weak cryptography", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
557 -                        description: format!("{}: Weak cryptography", description),
557 +                        description: format!("{description}: Weak cryptography"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:593:37
    |
593 |                        description: format!("{}: Authentication issue", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
593 -                        description: format!("{}: Authentication issue", description),
593 +                        description: format!("{description}: Authentication issue"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:629:37
    |
629 |                        description: format!("{}: Security misconfiguration", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
629 -                        description: format!("{}: Security misconfiguration", description),
629 +                        description: format!("{description}: Security misconfiguration"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:691:37
    |
691 |                        description: format!("{}: Potential race condition", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
691 -                        description: format!("{}: Potential race condition", description),
691 +                        description: format!("{description}: Potential race condition"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:729:37
    |
729 |                        description: format!("{}: Buffer overflow risk", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
729 -                        description: format!("{}: Buffer overflow risk", description),
729 +                        description: format!("{description}: Buffer overflow risk"),
    |

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/dependency/scanner.rs:55:39
   |
55 | ...                   severity: vuln_info.severity.clone(),
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `vuln_info.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/security/dependency/parsers/npm.rs:169:28
    |
169 |                 let name = path.split('/').last().unwrap_or(path);
    |                            ^^^^^^^^^^^^^^^^------
    |                                            |
    |                                            help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/python.rs:49:21
   |
49 |                     format!("{}{}", op, version)
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
49 -                     format!("{}{}", op, version)
49 +                     format!("{op}{version}")
   |

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/gradle.rs:64:28
   |
64 |                 let name = format!("{}:{}", group, artifact);
   |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -                 let name = format!("{}:{}", group, artifact);
64 +                 let name = format!("{group}:{artifact}");
   |

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
  --> src/services/security/dependency/parsers/dotnet.rs:30:21
   |
30 |                       for attr in e.attributes() {
   |                       ^           -------------- help: try: `e.attributes().flatten()`
   |  _____________________|
   | |
31 | |                         if let Ok(attr) = attr {
32 | |                             match attr.key.as_ref() {
33 | |                                 b"id" => name = String::from_utf8_lossy(&attr.value).to_string(),
...  |
41 | |                     }
   | |_____________________^
   |
help: ...and remove the `if let` statement in the for loop
  --> src/services/security/dependency/parsers/dotnet.rs:31:25
   |
31 | /                         if let Ok(attr) = attr {
32 | |                             match attr.key.as_ref() {
33 | |                                 b"id" => name = String::from_utf8_lossy(&attr.value).to_string(),
34 | |                                 b"version" => version = String::from_utf8_lossy(&attr.value).to_string(),
...  |
40 | |                         }
   | |_________________________^
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten
   = note: `#[warn(clippy::manual_flatten)]` on by default

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
   --> src/services/security/dependency/parsers/dotnet.rs:102:21
    |
102 |                       for attr in e.attributes() {
    |                       ^           -------------- help: try: `e.attributes().flatten()`
    |  _____________________|
    | |
103 | |                         if let Ok(attr) = attr {
104 | |                             match attr.key.as_ref() {
105 | |                                 b"Include" => name = String::from_utf8_lossy(&attr.value).to_string(),
...   |
113 | |                     }
    | |_____________________^
    |
help: ...and remove the `if let` statement in the for loop
   --> src/services/security/dependency/parsers/dotnet.rs:103:25
    |
103 | /                         if let Ok(attr) = attr {
104 | |                             match attr.key.as_ref() {
105 | |                                 b"Include" => name = String::from_utf8_lossy(&attr.value).to_string(),
106 | |                                 b"Version" => version = String::from_utf8_lossy(&attr.value).to_string(),
...   |
112 | |                         }
    | |_________________________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten

warning: accessing first element with `arr.get(0)`
  --> src/services/security/dependency/parsers/php.rs:99:33
   |
99 | ...                   arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
   |                       ^^^^^^^^^^ help: try: `arr.first()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#get_first
   = note: `#[warn(clippy::get_first)]` on by default

warning: accessing first element with `arr.get(0)`
   --> src/services/security/dependency/parsers/php.rs:141:33
    |
141 | ...                   arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
    |                       ^^^^^^^^^^ help: try: `arr.first()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#get_first

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/go.rs:69:42
   |
69 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
69 -                         current_version: format!("v{}", version),
69 +                         current_version: format!("v{version}"),
   |

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/go.rs:96:42
   |
96 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
96 -                         current_version: format!("v{}", version),
96 +                         current_version: format!("v{version}"),
   |

warning: variables can be used directly in the `format!` string
   --> src/services/security/dependency/parsers/go.rs:146:27
    |
146 |                 let key = format!("{}@{}", module, version);
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
146 -                 let key = format!("{}@{}", module, version);
146 +                 let key = format!("{module}@{version}");
    |

warning: this `map_or` can be simplified
  --> src/services/security/dependency/parsers/ruby.rs:53:30
   |
53 |                 let is_dev = current_group.as_ref().map_or(false, |g| g == "development" || g == "test");
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_some_and instead
   |
53 -                 let is_dev = current_group.as_ref().map_or(false, |g| g == "development" || g == "test");
53 +                 let is_dev = current_group.as_ref().is_some_and(|g| g == "development" || g == "test");
   |

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/secrets/detector.rs:52:35
   |
52 |                         severity: pattern.severity.clone(),
   |                                   ^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `pattern.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: this expression creates a reference which is immediately dereferenced by the compiler
  --> src/services/security/compliance/checker.rs:42:58
   |
42 |                         if self.is_likely_false_positive(&rule, &line_content) {
   |                                                          ^^^^^ help: change this to: `rule`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/compliance/checker.rs:53:39
   |
53 | ...                   severity: rule.severity.clone(),
   |                                 ^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `rule.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: use of `or_insert_with` to construct default value
  --> src/services/security/threat/modeler.rs:85:36
   |
85 |             vuln_groups.entry(key).or_insert_with(Vec::new).push(vuln);
   |                                    ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default
   = note: `#[warn(clippy::unwrap_or_default)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/security/threat/modeler.rs:102:30
    |
102 |                 threat_name: format!("{} Exploitation Threat", vuln_type),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
102 -                 threat_name: format!("{} Exploitation Threat", vuln_type),
102 +                 threat_name: format!("{vuln_type} Exploitation Threat"),
    |

warning: use of `or_insert_with` to construct default value
   --> src/services/security/threat/modeler.rs:142:62
    |
142 |             severity_groups.entry(dep_vuln.severity.clone()).or_insert_with(Vec::new).push(dep_vuln);
    |                                                              ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
   --> src/services/security/threat/modeler.rs:142:35
    |
142 |             severity_groups.entry(dep_vuln.severity.clone()).or_insert_with(Vec::new).push(dep_vuln);
    |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `dep_vuln.severity`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: variables can be used directly in the `format!` string
   --> src/services/security/threat/modeler.rs:155:30
    |
155 |                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
155 -                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
155 +                 threat_name: format!("Supply Chain Attack via {severity} Dependencies"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/threat/modeler.rs:290:9
    |
290 | /         format!(
291 | |             "Identified {} instances of {} vulnerabilities that could be exploited by attackers to compromise the system",
292 | |             count, vuln_type
293 | |         )
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: writing `&mut Vec` instead of `&mut [_]` involves a new object where a slice will do
   --> src/services/security/threat/modeler.rs:355:43
    |
355 |     fn prioritize_threats(&self, threats: &mut Vec<ThreatModel>) {
    |                                           ^^^^^^^^^^^^^^^^^^^^^ help: change this to: `&mut [ThreatModel]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#ptr_arg
    = note: `#[warn(clippy::ptr_arg)]` on by default

warning: you should consider adding a `Default` implementation for `SecurityAnalyzer`
  --> src/services/security/mod.rs:47:5
   |
47 | /     pub fn new() -> Self {
48 | |         Self {
49 | |             vulnerability_detector: VulnerabilityDetector::new(),
50 | |             dependency_scanner: DependencyScanner::new(),
...  |
56 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
46 + impl Default for SecurityAnalyzer {
47 +     fn default() -> Self {
48 +         Self::new()
49 +     }
50 + }
   |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:246:22
    |
246 |             message: format!("Failed to get database connection: {:?}", e),
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
246 -             message: format!("Failed to get database connection: {:?}", e),
246 +             message: format!("Failed to get database connection: {e:?}"),
    |

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/api/auth_extractor.rs:365:55
    |
365 |     let rate_limit = get_user_rate_limit(&claims.sub, &state)
    |                                                       ^^^^^^ help: change this to: `state`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/api/auth_extractor.rs:370:24
    |
370 |     audit_auth_success(&state, &claims.sub, AuthMethod::JwtToken).await;
    |                        ^^^^^^ help: change this to: `state`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/api/auth_extractor.rs:451:55
    |
451 |     let rate_limit = get_user_rate_limit(&claims.sub, &state)
    |                                                       ^^^^^^ help: change this to: `state`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/api/auth_extractor.rs:456:24
    |
456 |     audit_auth_success(&state, &claims.sub, AuthMethod::JwtToken).await;
    |                        ^^^^^^ help: change this to: `state`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:483:22
    |
483 |         .map_err(|e| format!("Database error: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
483 -         .map_err(|e| format!("Database error: {}", e))?;
483 +         .map_err(|e| format!("Database error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:488:22
    |
488 |         .map_err(|e| format!("Query error: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
488 -         .map_err(|e| format!("Query error: {}", e))?;
488 +         .map_err(|e| format!("Query error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:493:22
    |
493 |         .map_err(|e| format!("Read error: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
493 -         .map_err(|e| format!("Read error: {}", e))?
493 +         .map_err(|e| format!("Read error: {e}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:497:26
    |
497 |             .map_err(|e| format!("Failed to get user_id: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
497 -             .map_err(|e| format!("Failed to get user_id: {}", e))?;
497 +             .map_err(|e| format!("Failed to get user_id: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:500:26
    |
500 |             .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
500 -             .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
500 +             .map_err(|e| format!("Failed to get api_key_hash: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:503:26
    |
503 |             .map_err(|e| format!("Failed to get salt: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
503 -             .map_err(|e| format!("Failed to get salt: {}", e))?;
503 +             .map_err(|e| format!("Failed to get salt: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:506:26
    |
506 |             .map_err(|e| format!("Failed to get rate_limit: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
506 -             .map_err(|e| format!("Failed to get rate_limit: {}", e))?;
506 +             .map_err(|e| format!("Failed to get rate_limit: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/auth_extractor.rs:523:22
    |
523 |         .map_err(|e| format!("Failed to decode salt: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
523 -         .map_err(|e| format!("Failed to decode salt: {}", e))?;
523 +         .map_err(|e| format!("Failed to decode salt: {e}"))?;
    |

warning: `to_string` applied to a type that implements `Display` in `format!` args
   --> src/api/errors.rs:115:43
    |
115 |             error_id: format!("error_{}", Uuid::new_v4().to_string().replace("-", "")[..16].to_string()),
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: use this: `&Uuid::new_v4().to_string().replace("-", "")[..16]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#to_string_in_format_args
    = note: `#[warn(clippy::to_string_in_format_args)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:287:42
    |
287 |                     ApiError::BadRequest(format!("File too large: {} bytes (limit: {} bytes)", size, limit)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
287 -                     ApiError::BadRequest(format!("File too large: {} bytes (limit: {} bytes)", size, limit)),
287 +                     ApiError::BadRequest(format!("File too large: {size} bytes (limit: {limit} bytes)")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:289:50
    |
289 |                     ApiError::ServiceUnavailable(format!("Parse timeout after {} seconds", timeout_seconds)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
289 -                     ApiError::ServiceUnavailable(format!("Parse timeout after {} seconds", timeout_seconds)),
289 +                     ApiError::ServiceUnavailable(format!("Parse timeout after {timeout_seconds} seconds")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:291:42
    |
291 |                     ApiError::BadRequest(format!("Invalid encoding: {}", msg)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
291 -                     ApiError::BadRequest(format!("Invalid encoding: {}", msg)),
291 +                     ApiError::BadRequest(format!("Invalid encoding: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:293:42
    |
293 |                     ApiError::BadRequest(format!("Dependency parsing error: {}", msg)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
293 -                     ApiError::BadRequest(format!("Dependency parsing error: {}", msg)),
293 +                     ApiError::BadRequest(format!("Dependency parsing error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:295:45
    |
295 |                     ApiError::InternalError(format!("AST traversal error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
295 -                     ApiError::InternalError(format!("AST traversal error: {}", msg)),
295 +                     ApiError::InternalError(format!("AST traversal error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:297:50
    |
297 |                     ApiError::ServiceUnavailable(format!("Parser pool error: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
297 -                     ApiError::ServiceUnavailable(format!("Parser pool error: {}", msg)),
297 +                     ApiError::ServiceUnavailable(format!("Parser pool error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:302:50
    |
302 |                     ApiError::ServiceUnavailable(format!("Database connection failed: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
302 -                     ApiError::ServiceUnavailable(format!("Database connection failed: {}", msg)),
302 +                     ApiError::ServiceUnavailable(format!("Database connection failed: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:304:50
    |
304 |                     ApiError::ServiceUnavailable(format!("Query failed: {} - {}", query, reason)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
304 -                     ApiError::ServiceUnavailable(format!("Query failed: {} - {}", query, reason)),
304 +                     ApiError::ServiceUnavailable(format!("Query failed: {query} - {reason}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:306:50
    |
306 |                     ApiError::ServiceUnavailable(format!("Transaction failed: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
306 -                     ApiError::ServiceUnavailable(format!("Transaction failed: {}", msg)),
306 +                     ApiError::ServiceUnavailable(format!("Transaction failed: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:308:50
    |
308 |                     ApiError::ServiceUnavailable(format!("Cache error: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
308 -                     ApiError::ServiceUnavailable(format!("Cache error: {}", msg)),
308 +                     ApiError::ServiceUnavailable(format!("Cache error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:310:45
    |
310 |                     ApiError::InternalError(format!("Serialization error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
310 -                     ApiError::InternalError(format!("Serialization error: {}", msg)),
310 +                     ApiError::InternalError(format!("Serialization error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:312:45
    |
312 |                     ApiError::InternalError(format!("Deserialization error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
312 -                     ApiError::InternalError(format!("Deserialization error: {}", msg)),
312 +                     ApiError::InternalError(format!("Deserialization error: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:316:40
    |
316 |                     ApiError::Conflict(format!("Constraint violation: {}", msg)),
    |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
316 -                     ApiError::Conflict(format!("Constraint violation: {}", msg)),
316 +                     ApiError::Conflict(format!("Constraint violation: {msg}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/errors.rs:320:50
    |
320 |                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {} / {}", current, limit)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
320 -                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {} / {}", current, limit)),
320 +                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {current} / {limit}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:187:59
    |
187 |                       return (ErrorResponse::internal_error(format!(
    |  ___________________________________________________________^
188 | |                         "Failed to get Spanner connection: {:?}",
189 | |                         e
190 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:235:59
    |
235 |                       return (ErrorResponse::internal_error(format!(
    |  ___________________________________________________________^
236 | |                         "Failed to get Spanner connection: {:?}",
237 | |                         e
238 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:277:58
    |
277 |                       return ErrorResponse::internal_error(format!(
    |  __________________________________________________________^
278 | |                         "Failed to get Spanner connection: {:?}",
279 | |                         e
280 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:338:58
    |
338 |                       return ErrorResponse::internal_error(format!(
    |  __________________________________________________________^
339 | |                         "Failed to get Spanner connection: {:?}",
340 | |                         e
341 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:430:58
    |
430 |                       return ErrorResponse::internal_error(format!(
    |  __________________________________________________________^
431 | |                         "Failed to get Spanner connection: {:?}",
432 | |                         e
433 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: this `map_or` can be simplified
   --> src/api/handlers/analysis.rs:452:29
    |
452 | / ...                   w.file_path
453 | | ...                       .as_ref()
454 | | ...                       .map_or(false, |p| p.contains(file_path))
    | |___________________________________________________________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_some_and instead
    |
454 -                                 .map_or(false, |p| p.contains(file_path))
454 +                                 .is_some_and(|p| p.contains(file_path))
    |

warning: using `clone` on type `WarningType` which implements the `Copy` trait
   --> src/api/handlers/analysis.rs:467:36
    |
467 | ...                   .entry(warning.warning_type.clone())
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `warning.warning_type`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:515:58
    |
515 |                       return ErrorResponse::internal_error(format!(
    |  __________________________________________________________^
516 | |                         "Failed to get Spanner connection: {:?}",
517 | |                         e
518 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:557:59
    |
557 |                       return (ErrorResponse::internal_error(format!(
    |  ___________________________________________________________^
558 | |                         "Failed to get Spanner connection: {:?}",
559 | |                         e
560 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/api/handlers/security.rs:55:39
   |
55 |           return Err(ApiError::NotFound(format!(
   |  _______________________________________^
56 | |             "Analysis {} not found",
57 | |             analysis_id
58 | |         )));
   | |_________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:180:40
    |
180 |           None => Err(ApiError::NotFound(format!(
    |  ________________________________________^
181 | |             "Security analysis not found for analysis {}",
182 | |             analysis_id
183 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:302:40
    |
302 |           None => Err(ApiError::NotFound(format!(
    |  ________________________________________^
303 | |             "Security assessment not found for analysis {}",
304 | |             analysis_id
305 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:328:40
    |
328 |           None => Err(ApiError::NotFound(format!(
    |  ________________________________________^
329 | |             "Security metadata not found for analysis {}",
330 | |             analysis_id
331 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: this `map_or` can be simplified
   --> src/api/middleware/auth_layer.rs:165:30
    |
165 |             .retain(|_, key| key.expires_at.map_or(true, |expires| expires > now));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_none_or instead
    |
165 -             .retain(|_, key| key.expires_at.map_or(true, |expires| expires > now));
165 +             .retain(|_, key| key.expires_at.is_none_or(|expires| expires > now));
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:475:22
    |
475 |         .map_err(|e| format!("Failed to get Spanner connection from pool: {:?}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
475 -         .map_err(|e| format!("Failed to get Spanner connection from pool: {:?}", e))?;
475 +         .map_err(|e| format!("Failed to get Spanner connection from pool: {e:?}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:497:22
    |
497 |         .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
497 -         .map_err(|e| format!("Failed to create read transaction: {}", e))?;
497 +         .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:501:22
    |
501 |         .map_err(|e| format!("Failed to query API keys: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
501 -         .map_err(|e| format!("Failed to query API keys: {}", e))?;
501 +         .map_err(|e| format!("Failed to query API keys: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:508:22
    |
508 |         .map_err(|e| format!("Failed to read row: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
508 -         .map_err(|e| format!("Failed to read row: {}", e))?
508 +         .map_err(|e| format!("Failed to read row: {e}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:512:26
    |
512 |             .map_err(|e| format!("Failed to read key_hash: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
512 -             .map_err(|e| format!("Failed to read key_hash: {}", e))?;
512 +             .map_err(|e| format!("Failed to read key_hash: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:515:26
    |
515 |             .map_err(|e| format!("Failed to read salt: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
515 -             .map_err(|e| format!("Failed to read salt: {}", e))?;
515 +             .map_err(|e| format!("Failed to read salt: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:524:38
    |
524 |                         .map_err(|e| format!("Invalid expiration time format: {}", e))?;
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
524 -                         .map_err(|e| format!("Invalid expiration time format: {}", e))?;
524 +                         .map_err(|e| format!("Invalid expiration time format: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:532:34
    |
532 |                     .map_err(|e| format!("Failed to read user_id: {}", e))?;
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
532 -                     .map_err(|e| format!("Failed to read user_id: {}", e))?;
532 +                     .map_err(|e| format!("Failed to read user_id: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:535:34
    |
535 |                     .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
535 -                     .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
535 +                     .map_err(|e| format!("Failed to read rate_limit: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:567:22
    |
567 |         .map_err(|e| format!("Failed to decode salt: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
567 -         .map_err(|e| format!("Failed to decode salt: {}", e))?;
567 +         .map_err(|e| format!("Failed to decode salt: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:622:51
    |
622 |     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {}", e))?;
    |                                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
622 -     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {}", e))?;
622 +     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:631:28
    |
631 |             .ok_or_else(|| format!("Unknown key ID: {}", kid))?
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
631 -             .ok_or_else(|| format!("Unknown key ID: {}", kid))?
631 +             .ok_or_else(|| format!("Unknown key ID: {kid}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:667:18
    |
667 |             _ => format!("Token validation failed: {}", e),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
667 -             _ => format!("Token validation failed: {}", e),
667 +             _ => format!("Token validation failed: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:707:22
    |
707 |         .map_err(|e| format!("System time error: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
707 -         .map_err(|e| format!("System time error: {}", e))?
707 +         .map_err(|e| format!("System time error: {e}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:755:30
    |
755 |                 .map_err(|e| format!("Failed to get spanner connection: {:?}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
755 -                 .map_err(|e| format!("Failed to get spanner connection: {:?}", e))?;
755 +                 .map_err(|e| format!("Failed to get spanner connection: {e:?}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:760:30
    |
760 |                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
760 -                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
760 +                 .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:764:30
    |
764 |                 .map_err(|e| format!("Failed to query user: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
764 -                 .map_err(|e| format!("Failed to query user: {}", e))?;
764 +                 .map_err(|e| format!("Failed to query user: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:768:30
    |
768 |                 .map_err(|e| format!("Failed to read row: {}", e))?
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
768 -                 .map_err(|e| format!("Failed to read row: {}", e))?
768 +                 .map_err(|e| format!("Failed to read row: {e}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:778:26
    |
778 |             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
778 -             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
778 +             .map_err(|e| format!("Failed to read rate_limit: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:849:5
    |
849 |     format!("{:x}", result)
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
849 -     format!("{:x}", result)
849 +     format!("{result:x}")
    |

warning: variables can be used directly in the `format!` string
    --> src/api/middleware/auth_layer.rs:1091:22
     |
1091 |         description: format!("Wait {} seconds before making another request", retry_after),
     |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1091 -         description: format!("Wait {} seconds before making another request", retry_after),
1091 +         description: format!("Wait {retry_after} seconds before making another request"),
     |

warning: variables can be used directly in the `format!` string
    --> src/api/middleware/auth_layer.rs:1139:15
     |
1139 |     let key = format!("rate_limit:{}", user_id);
     |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1139 -     let key = format!("rate_limit:{}", user_id);
1139 +     let key = format!("rate_limit:{user_id}");
     |

warning: variables can be used directly in the `format!` string
  --> src/api/rate_limit_extractor.rs:68:17
   |
68 |                 format!("Rate limit check failed: {}", e),
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
68 -                 format!("Rate limit check failed: {}", e),
68 +                 format!("Rate limit check failed: {e}"),
   |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:173:15
    |
173 |     let key = format!("rate_limit:{}", user_id);
    |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
173 -     let key = format!("rate_limit:{}", user_id);
173 +     let key = format!("rate_limit:{user_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:190:63
    |
190 |     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {}", e))?;
    |                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
190 -     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {}", e))?;
190 +     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:194:65
    |
194 |         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {}", e))?;
    |                                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
194 -         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {}", e))?;
194 +         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:248:9
    |
248 |         format!("{} seconds", seconds_until_reset)
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
248 -         format!("{} seconds", seconds_until_reset)
248 +         format!("{seconds_until_reset} seconds")
    |

warning: this match could be written as a `let` statement
  --> src/api/mod.rs:49:9
   |
49 | /         let redis_pool = match RedisConnectionManager::new(config.redis.clone()) {
50 | |             manager => match Pool::builder().build(manager).await {
51 | |                 Ok(pool) => {
52 | |                     tracing::info!("Redis connection pool created successfully");
...  |
60 | |         };
   | |__________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#match_single_binding
   = note: `#[warn(clippy::match_single_binding)]` on by default
help: consider using a `let` statement
   |
49 ~         let manager = RedisConnectionManager::new(config.redis.clone());
50 +         let redis_pool = match Pool::builder().build(manager).await {
51 +             Ok(pool) => {
52 +                 tracing::info!("Redis connection pool created successfully");
53 +                 Some(Arc::new(pool))
54 +             }
55 +             Err(e) => {
56 +                 tracing::warn!("Failed to create Redis connection pool: {}. Continuing without Redis caching.", e);
57 +                 None
58 +             }
59 +         };
   |

warning: `format!` in `format!` args
   --> src/audit/mod.rs:254:32
    |
254 |               anyhow::Error::msg(anyhow::__private::format!(
    |  ________________________________^
255 | |                 "Failed to get spanner connection for audit log: {}",
256 | |                 format!("{:?}", e)
257 | |             ))
    | |_____________^
    |
    = help: combine the `format!(..)` arguments with the outer `format!(..)` call
    = help: or consider changing `format!` to `format_args!`
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#format_in_format_args
    = note: `#[warn(clippy::format_in_format_args)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/storage/spanner.rs:442:24
    |
442 |         let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -         let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);
442 +         let database = format!("projects/{project_id}/instances/{instance_id}/databases/{database_id}");
    |

warning: variables can be used directly in the `format!` string
    --> src/storage/spanner.rs:1344:29
     |
1344 |             query.push_str(&format!(" LIMIT {}", limit));
     |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1344 -             query.push_str(&format!(" LIMIT {}", limit));
1344 +             query.push_str(&format!(" LIMIT {limit}"));
     |

warning: module has the same name as its containing module
 --> src/storage/mod.rs:3:1
  |
3 | pub mod storage;
  | ^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception
  = note: `#[warn(clippy::module_inception)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/storage/storage.rs:21:33
   |
21 |             .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
21 -             .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
21 +             .unwrap_or_else(|_| format!("ccl-analysis-{project_id}"));
   |

warning: variables can be used directly in the `format!` string
  --> src/storage/storage.rs:50:27
   |
50 |         let object_name = format!("analysis_results/{}.json", analysis_id);
   |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
50 -         let object_name = format!("analysis_results/{}.json", analysis_id);
50 +         let object_name = format!("analysis_results/{analysis_id}.json");
   |

warning: variables can be used directly in the `format!` string
  --> src/storage/storage.rs:94:27
   |
94 |         let object_name = format!("analysis_results/{}.json", analysis_id);
   |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
94 -         let object_name = format!("analysis_results/{}.json", analysis_id);
94 +         let object_name = format!("analysis_results/{analysis_id}.json");
   |

warning: variables can be used directly in the `format!` string
  --> src/storage/pubsub.rs:35:30
   |
35 |             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
35 -             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
35 +             let topic_path = format!("projects/{project_id}/topics/{topic_name}");
   |

warning: variables can be used directly in the `format!` string
   --> src/storage/pubsub.rs:186:39
    |
186 |                         warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
    |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
186 -                         warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
186 +                         warnings.push(format!("Cannot verify topic {name} due to missing pubsub.topics.get permission"));
    |

warning: variables can be used directly in the `format!` string
  --> src/storage/redis_client.rs:55:30
   |
55 |         let rate_limit_key = format!("rate_limit:{}", key);
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
55 -         let rate_limit_key = format!("rate_limit:{}", key);
55 +         let rate_limit_key = format!("rate_limit:{key}");
   |

warning: variables can be used directly in the `format!` string
   --> src/storage/redis_client.rs:152:31
    |
152 |         let _rate_limit_key = format!("rate_limit:{}", key);
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
152 -         let _rate_limit_key = format!("rate_limit:{}", key);
152 +         let _rate_limit_key = format!("rate_limit:{key}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/redis_client.rs:167:29
    |
167 |             let burst_key = format!("burst:{}", key);
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
167 -             let burst_key = format!("burst:{}", key);
167 +             let burst_key = format!("burst:{key}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/redis_client.rs:198:30
    |
198 |         let rate_limit_key = format!("rate_limit:{}", key);
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
198 -         let rate_limit_key = format!("rate_limit:{}", key);
198 +         let rate_limit_key = format!("rate_limit:{key}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/redis_client.rs:230:30
    |
230 |         let rate_limit_key = format!("rate_limit:{}", key);
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
230 -         let rate_limit_key = format!("rate_limit:{}", key);
230 +         let rate_limit_key = format!("rate_limit:{key}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/redis_client.rs:231:25
    |
231 |         let burst_key = format!("burst:{}", key);
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
231 -         let burst_key = format!("burst:{}", key);
231 +         let burst_key = format!("burst:{key}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:366:29
    |
366 |             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
366 -             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
366 +             let cache_key = format!("{ANALYSIS_CACHE_PREFIX}{repo_url}:main");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:397:19
    |
397 |         let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
397 -         let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
397 +         let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:504:23
    |
504 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
504 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
504 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:537:23
    |
537 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
537 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
537 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:556:23
    |
556 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
556 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
556 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:584:23
    |
584 |             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
584 -             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
584 +             let key = format!("{PATTERN_CACHE_PREFIX}{file_hash}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:605:23
    |
605 |             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
605 -             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
605 +             let key = format!("{PATTERN_CACHE_PREFIX}{file_hash}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:625:23
    |
625 |             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
625 -             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
625 +             let key = format!("{EMBEDDING_CACHE_PREFIX}{chunk_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:646:23
    |
646 |             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
646 -             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
646 +             let key = format!("{EMBEDDING_CACHE_PREFIX}{chunk_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:672:23
    |
672 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
672 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
672 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |

warning: you should consider adding a `Default` implementation for `GitService`
  --> src/git/mod.rs:11:5
   |
11 | /     pub fn new() -> Self {
12 | |         Self
13 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
10 + impl Default for GitService {
11 +     fn default() -> Self {
12 +         Self::new()
13 +     }
14 + }
   |

warning: variables can be used directly in the `format!` string
  --> src/git/mod.rs:21:40
   |
21 |         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
   |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
21 -         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
21 +         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{analysis_id}"));
   |

warning: variables can be used directly in the `format!` string
  --> src/git/mod.rs:32:32
   |
32 |                 let key_path = format!("{}/.ssh/id_rsa", home);
   |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
32 -                 let key_path = format!("{}/.ssh/id_rsa", home);
32 +                 let key_path = format!("{home}/.ssh/id_rsa");
   |

warning: variables can be used directly in the `format!` string
  --> src/git/mod.rs:55:22
   |
55 |             .context(format!("Failed to clone repository: {}", url))?;
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
55 -             .context(format!("Failed to clone repository: {}", url))?;
55 +             .context(format!("Failed to clone repository: {url}"))?;
   |

warning: variables can be used directly in the `format!` string
  --> src/git/mod.rs:86:39
   |
86 |         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{}", temp_id));
   |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
86 -         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{}", temp_id));
86 +         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{temp_id}"));
   |

warning: variables can be used directly in the `format!` string
   --> src/git/mod.rs:105:32
    |
105 |                 let key_path = format!("{}/.ssh/id_rsa", home);
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
105 -                 let key_path = format!("{}/.ssh/id_rsa", home);
105 +                 let key_path = format!("{home}/.ssh/id_rsa");
    |

warning: variables can be used directly in the `format!` string
   --> src/git/mod.rs:131:22
    |
131 |             .context(format!("Failed to shallow clone repository for hash check: {}", url))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
131 -             .context(format!("Failed to shallow clone repository for hash check: {}", url))?;
131 +             .context(format!("Failed to shallow clone repository for hash check: {url}"))?;
    |

warning: this `if let` can be collapsed into the outer `match`
   --> src/parser/adapters.rs:108:13
    |
108 | /             if let Some(name) = name {
109 | |                 symbols.push(Symbol {
110 | |                     name: name.to_string(),
111 | |                     symbol_type: SymbolType::Variable, // Indexes as variables
...   |
119 | |                 });
120 | |             }
    | |_____________^
    |
help: the outer pattern can be modified to include the inner pattern
   --> src/parser/adapters.rs:107:34
    |
107 |         Statement::CreateIndex { name, .. } => {
    |                                  ^^^^ replace this binding
108 |             if let Some(name) = name {
    |                    ^^^^^^^^^^ with this pattern, prefixed by `name`:
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_match
    = note: `#[warn(clippy::collapsible_match)]` on by default

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/parser/adapters.rs:163:39
    |
163 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast
    = note: `#[warn(clippy::unnecessary_cast)]` on by default

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/parser/adapters.rs:168:39
    |
168 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
   --> src/parser/adapters.rs:179:21
    |
179 |                       for attr in e.attributes() {
    |                       ^           -------------- help: try: `e.attributes().flatten()`
    |  _____________________|
    | |
180 | |                         if let Ok(attr) = attr {
181 | |                             let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
182 | |                             symbols.push(Symbol {
...   |
194 | |                     }
    | |_____________________^
    |
help: ...and remove the `if let` statement in the for loop
   --> src/parser/adapters.rs:180:25
    |
180 | /                         if let Ok(attr) = attr {
181 | |                             let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
182 | |                             symbols.push(Symbol {
183 | |                                 name: format!("{}.{}", name, attr_name),
...   |
192 | |                             });
193 | |                         }
    | |_________________________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:183:39
    |
183 | ...                   name: format!("{}.{}", name, attr_name),
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
183 -                                 name: format!("{}.{}", name, attr_name),
183 +                                 name: format!("{name}.{attr_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:209:34
    |
209 |                         message: format!("XML parse error: {}", e),
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
209 -                         message: format!("XML parse error: {}", e),
209 +                         message: format!("XML parse error: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:241:26
    |
241 |                 message: format!("TOML parse error: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
241 -                 message: format!("TOML parse error: {}", e),
241 +                 message: format!("TOML parse error: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:255:21
    |
255 |                     format!("{}.{}", prefix, key)
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
255 -                     format!("{}.{}", prefix, key)
255 +                     format!("{prefix}.{key}")
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:353:31
    |
353 |                         name: format!("code_block_{}", lang),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
353 -                         name: format!("code_block_{}", lang),
353 +                         name: format!("code_block_{lang}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:369:45
    |
369 |                         documentation: Some(format!("Code block in {}", lang)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
369 -                         documentation: Some(format!("Code block in {}", lang)),
369 +                         documentation: Some(format!("Code block in {lang}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:420:37
    |
420 |                     signature: Some(format!("H{} {}", level, title)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
420 -                     signature: Some(format!("H{} {}", level, title)),
420 +                     signature: Some(format!("H{level} {title}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:421:41
    |
421 |                     documentation: Some(format!("Heading level {} in documentation", level)),
    |                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
421 -                     documentation: Some(format!("Heading level {} in documentation", level)),
421 +                     documentation: Some(format!("Heading level {level} in documentation")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:433:27
    |
433 |                     name: format!("task: {}", task_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
433 -                     name: format!("task: {}", task_text),
433 +                     name: format!("task: {task_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:460:27
    |
460 |                     name: format!("list_item: {}", item_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
460 -                     name: format!("list_item: {}", item_text),
460 +                     name: format!("list_item: {item_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:475:37
    |
475 |                     signature: Some(format!("List item: {}", item_text)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
475 -                     signature: Some(format!("List item: {}", item_text)),
475 +                     signature: Some(format!("List item: {item_text}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:485:31
    |
485 |                         name: format!("table_row_{}", current_line),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
485 -                         name: format!("table_row_{}", current_line),
485 +                         name: format!("table_row_{current_line}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:512:27
    |
512 |                     name: format!("link: {}", link_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
512 -                     name: format!("link: {}", link_text),
512 +                     name: format!("link: {link_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:527:37
    |
527 |                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
527 -                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
527 +                     signature: Some(format!("Link: {link_text} -> {link_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:538:27
    |
538 |                     name: format!("image: {}", alt_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
538 -                     name: format!("image: {}", alt_text),
538 +                     name: format!("image: {alt_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:553:37
    |
553 |                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
553 -                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
553 +                     signature: Some(format!("Image: {alt_text} -> {image_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:564:27
    |
564 |                     name: format!("reference: {}", ref_name),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
564 -                     name: format!("reference: {}", ref_name),
564 +                     name: format!("reference: {ref_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:579:37
    |
579 |                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
579 -                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
579 +                     signature: Some(format!("Reference: {ref_name} -> {ref_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:589:27
    |
589 |                     name: format!("inline_code: {}", code),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
589 -                     name: format!("inline_code: {}", code),
589 +                     name: format!("inline_code: {code}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:604:37
    |
604 |                     signature: Some(format!("Inline code: {}", code)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
604 -                     signature: Some(format!("Inline code: {}", code)),
604 +                     signature: Some(format!("Inline code: {code}")),
    |

warning: you should consider adding a `Default` implementation for `LanguageMetricsCalculator`
  --> src/parser/language_metrics.rs:61:5
   |
61 | /     pub fn new() -> Self {
62 | |         let mut calculator = Self {
63 | |             language_patterns: HashMap::new(),
64 | |         };
65 | |         calculator.initialize_patterns();
66 | |         calculator
67 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
60 + impl Default for LanguageMetricsCalculator {
61 +     fn default() -> Self {
62 +         Self::new()
63 +     }
64 + }
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:13:13
   |
13 |             println!("❌ Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
13 -             println!("❌ Failed to create parser: {}", e);
13 +             println!("❌ Failed to create parser: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:64:21
   |
64 |                     println!("✅ {} → {}", filename, detected_lang);
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -                     println!("✅ {} → {}", filename, detected_lang);
64 +                     println!("✅ {filename} → {detected_lang}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:67:21
   |
67 | /                     println!(
68 | |                         "⚠️  {} → {} (expected {})",
69 | |                         filename, detected_lang, expected_lang
70 | |                     );
   | |_____________________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:81:5
   |
81 |     println!("Successfully detected: {}", detected_count);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
81 -     println!("Successfully detected: {}", detected_count);
81 +     println!("Successfully detected: {detected_count}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:93:9
   |
93 |         println!("• {}", lang);
   |         ^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
93 -         println!("• {}", lang);
93 +         println!("• {lang}");
   |

warning: variables can be used directly in the `format!` string
   --> src/parser/validation_demo.rs:110:18
    |
110 |             _ => format!("test.{}", lang),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
110 -             _ => format!("test.{}", lang),
110 +             _ => format!("test.{lang}"),
    |

warning: variables can be used directly in the `format!` string
  --> src/parser/ast/chunk_extractor.rs:94:24
   |
94 |         let chunk_id = format!("chunk_{:016x}", counter);
   |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
94 -         let chunk_id = format!("chunk_{:016x}", counter);
94 +         let chunk_id = format!("chunk_{counter:016x}");
   |

warning: this `if` statement can be collapsed
   --> src/parser/language_detection.rs:115:13
    |
115 | /             if ext == "h" {
116 | |                 if self.is_cpp_header(content) {
117 | |                     return Ok(Some("cpp".to_string()));
118 | |                 }
119 | |             }
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if
    = note: `#[warn(clippy::collapsible_if)]` on by default
help: collapse nested if block
    |
115 ~             if ext == "h"
116 ~                 && self.is_cpp_header(content) {
117 |                     return Ok(Some("cpp".to_string()));
118 ~                 }
    |

warning: this `if` statement can be collapsed
   --> src/parser/language_detection.rs:156:9
    |
156 | /         if sample.trim_start().starts_with("<?xml") || sample.trim_start().starts_with("<") {
157 | |             if self.looks_like_xml(sample) {
158 | |                 return Ok(Some("xml".to_string()));
159 | |             }
160 | |         }
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if
help: collapse nested if block
    |
156 ~         if (sample.trim_start().starts_with("<?xml") || sample.trim_start().starts_with("<")) {
157 ~             && self.looks_like_xml(sample) {
158 |                 return Ok(Some("xml".to_string()));
159 ~             }
    |

warning: redundant closure
  --> src/parser/language_registry.rs:30:15
   |
30 |     Lazy::new(|| HashMap::new());
   |               ^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `HashMap::new`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure
   = note: `#[warn(clippy::redundant_closure)]` on by default

warning: manual implementation of `Option::map`
  --> src/parser/streaming/file_processor.rs:81:31
   |
81 |           let _monitor_handle = if let Some(monitor) = &self.memory_monitor {
   |  _______________________________^
82 | |             Some(monitor.start_monitoring())
83 | |         } else {
84 | |             None
85 | |         };
   | |_________^ help: try: `self.memory_monitor.as_ref().map(|monitor| monitor.start_monitoring())`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_map
   = note: `#[warn(clippy::manual_map)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/parser/streaming/hasher.rs:24:9
   |
24 |         format!("{:x}", result)
   |         ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
24 -         format!("{:x}", result)
24 +         format!("{result:x}")
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/mod.rs:98:26
   |
98 |                 message: format!("Failed to read file metadata: {}", e),
   |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
98 -                 message: format!("Failed to read file metadata: {}", e),
98 +                 message: format!("Failed to read file metadata: {e}"),
   |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:153:30
    |
153 |                     message: format!("Failed to detect language: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
153 -                     message: format!("Failed to detect language: {}", e),
153 +                     message: format!("Failed to detect language: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:159:30
    |
159 |                     message: format!("Could not detect language for file: {}", file_name),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
159 -                     message: format!("Could not detect language for file: {}", file_name),
159 +                     message: format!("Could not detect language for file: {file_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:177:26
    |
177 |                 message: format!("Failed to get parser from pool: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
177 -                 message: format!("Failed to get parser from pool: {}", e),
177 +                 message: format!("Failed to get parser from pool: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:230:26
    |
230 |                 message: format!("Failed to read file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
230 -                 message: format!("Failed to read file: {}", e),
230 +                 message: format!("Failed to read file: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:268:26
    |
268 |                 message: format!("No custom parser for language: {}", language),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
268 -                 message: format!("No custom parser for language: {}", language),
268 +                 message: format!("No custom parser for language: {language}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:459:26
    |
459 |                 message: format!("Language not supported: {}", language_name),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
459 -                 message: format!("Language not supported: {}", language_name),
459 +                 message: format!("Language not supported: {language_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:469:30
    |
469 |                     message: format!("Failed to create parser pool: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
469 -                     message: format!("Failed to create parser pool: {}", e),
469 +                     message: format!("Failed to create parser pool: {e}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/mod.rs:496:30
    |
496 |                     message: format!("Failed to preload parsers: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
496 -                     message: format!("Failed to preload parsers: {}", e),
496 +                     message: format!("Failed to preload parsers: {e}"),
    |

warning: you should consider adding a `Default` implementation for `GranularMetricsCollector`
   --> src/metrics/granular.rs:223:5
    |
223 | /     pub fn new() -> Self {
224 | |         Self {
225 | |             operation_metrics: Arc::new(RwLock::new(HashMap::new())),
226 | |             language_metrics: Arc::new(RwLock::new(HashMap::new())),
...   |
232 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
221 + impl Default for GranularMetricsCollector {
222 +     fn default() -> Self {
223 +         Self::new()
224 +     }
225 + }
    |

warning: this `impl` can be derived
   --> src/metrics/granular.rs:535:1
    |
535 | / impl Default for ConcurrencyMetrics {
536 | |     fn default() -> Self {
537 | |         Self {
538 | |             active_analyses: 0,
...   |
553 | | }
    | |_^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
help: replace the manual implementation with a derive attribute
    |
155 + #[derive(Default)]
156 ~ pub struct ConcurrencyMetrics {
    |

warning: this `impl` can be derived
   --> src/metrics/granular.rs:555:1
    |
555 | / impl Default for ErrorMetrics {
556 | |     fn default() -> Self {
557 | |         Self {
558 | |             parser_errors: 0,
...   |
574 | | }
    | |_^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
help: replace the manual implementation with a derive attribute
    |
179 + #[derive(Default)]
180 ~ pub struct ErrorMetrics {
    |

warning: you should consider adding a `Default` implementation for `MetricsCollector`
   --> src/metrics/mod.rs:364:5
    |
364 | /     pub fn new() -> Self {
365 | |         Self {
366 | |             current_metrics: PerformanceMetrics::default(),
367 | |             metrics_history: Vec::new(),
...   |
372 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
363 + impl Default for MetricsCollector {
364 +     fn default() -> Self {
365 +         Self::new()
366 +     }
367 + }
    |

warning: use of `or_insert_with` to construct default value
   --> src/metrics/mod.rs:398:14
    |
398 |             .or_insert_with(Vec::new)
    |              ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default

warning: you should consider adding a `Default` implementation for `SystemResourceMonitor`
   --> src/metrics/mod.rs:421:5
    |
421 | /     pub fn new() -> Self {
422 | |         let monitor = Self {
423 | |             update_interval: Duration::from_secs(30),
424 | |             current_stats: Arc::new(RwLock::new(SystemStats::default())),
...   |
430 | |         monitor
431 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
420 + impl Default for SystemResourceMonitor {
421 +     fn default() -> Self {
422 +         Self::new()
423 +     }
424 + }
    |

warning: unneeded `return` statement
   --> src/metrics/mod.rs:523:13
    |
523 |             return Ok(MemoryInfo { total, used, available });
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_return
    = note: `#[warn(clippy::needless_return)]` on by default
help: remove `return`
    |
523 -             return Ok(MemoryInfo { total, used, available });
523 +             Ok(MemoryInfo { total, used, available })
    |

warning: you should consider adding a `Default` implementation for `MetricsService`
   --> src/metrics/mod.rs:801:5
    |
801 | /     pub fn new() -> Self {
802 | |         Self
803 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
800 + impl Default for MetricsService {
801 +     fn default() -> Self {
802 +         Self::new()
803 +     }
804 + }
    |

warning: redundant closure
   --> src/contracts.rs:208:43
    |
208 |                         chunks.iter().map(|chunk| convert_code_chunk(chunk)).collect()
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `convert_code_chunk`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure

warning: redundant closure
   --> src/contracts.rs:211:44
    |
211 |                         symbols.iter().map(|symbol| convert_symbol(symbol)).collect()
    |                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `convert_symbol`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure

warning: variables can be used directly in the `format!` string
   --> src/contracts.rs:442:56
    |
442 |         visibility: symbol.visibility.as_ref().map(|v| format!("{:?}", v)),
    |                                                        ^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -         visibility: symbol.visibility.as_ref().map(|v| format!("{:?}", v)),
442 +         visibility: symbol.visibility.as_ref().map(|v| format!("{v:?}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:193:33
    |
193 |         AnalysisError::Internal(format!("IO error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
193 -         AnalysisError::Internal(format!("IO error: {}", err))
193 +         AnalysisError::Internal(format!("IO error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:199:33
    |
199 |         ParserError::TreeSitter(format!("IO error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
199 -         ParserError::TreeSitter(format!("IO error: {}", err))
199 +         ParserError::TreeSitter(format!("IO error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:205:40
    |
205 |         StorageError::ConnectionFailed(format!("IO error: {}", err))
    |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
205 -         StorageError::ConnectionFailed(format!("IO error: {}", err))
205 +         StorageError::ConnectionFailed(format!("IO error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:217:33
    |
217 |         AnalysisError::Internal(format!("JSON parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
217 -         AnalysisError::Internal(format!("JSON parsing error: {}", err))
217 +         AnalysisError::Internal(format!("JSON parsing error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:235:33
    |
235 |         AnalysisError::Internal(format!("TOML parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
235 -         AnalysisError::Internal(format!("TOML parsing error: {}", err))
235 +         AnalysisError::Internal(format!("TOML parsing error: {err}"))
    |

warning: this `if` has identical blocks
   --> src/errors.rs:258:36
    |
258 |           } else if err.is_connect() {
    |  ____________________________________^
259 | |             HttpError::Network(err.to_string())
260 | |         } else {
    | |_________^
    |
note: same as this
   --> src/errors.rs:260:16
    |
260 |           } else {
    |  ________________^
261 | |             HttpError::Network(err.to_string())
262 | |         }
    | |_________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#if_same_then_else
    = note: `#[warn(clippy::if_same_then_else)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:292:33
    |
292 |         AnalysisError::Internal(format!("XML parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
292 -         AnalysisError::Internal(format!("XML parsing error: {}", err))
292 +         AnalysisError::Internal(format!("XML parsing error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:298:33
    |
298 |         AnalysisError::Internal(format!("Regex error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
298 -         AnalysisError::Internal(format!("Regex error: {}", err))
298 +         AnalysisError::Internal(format!("Regex error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:304:33
    |
304 |         AnalysisError::Internal(format!("UTF-8 error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
304 -         AnalysisError::Internal(format!("UTF-8 error: {}", err))
304 +         AnalysisError::Internal(format!("UTF-8 error: {err}"))
    |

warning: variables can be used directly in the `format!` string
   --> src/errors.rs:412:37
    |
412 |             AnalysisError::Internal(format!("{}: {}", context, base_error))
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
412 -             AnalysisError::Internal(format!("{}: {}", context, base_error))
412 +             AnalysisError::Internal(format!("{context}: {base_error}"))
    |

warning: variables can be used directly in the `format!` string
  --> src/migrations/mod.rs:88:38
   |
88 |                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
   |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
88 -                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
88 +                     .with_context(|| format!("Failed to read migration file: {path:?}"))?;
   |

warning: `analysis-engine` (lib) generated 253 warnings (run `cargo clippy --fix --lib -p analysis-engine` to apply 241 suggestions)
warning: unused import: `tree_sitter::Language`
 --> src/bin/test_language_api.rs:2:5
  |
2 | use tree_sitter::Language;
  |     ^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_api.rs:19:13
   |
19 |             println!("✓ {}: get_language(\"{}\") works", name, name);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
19 -             println!("✓ {}: get_language(\"{}\") works", name, name);
19 +             println!("✓ {name}: get_language(\"{name}\") works");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_api.rs:23:13
   |
23 |             println!("✗ {}: get_language(\"{}\") failed", name, name);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
23 -             println!("✗ {}: get_language(\"{}\") failed", name, name);
23 +             println!("✗ {name}: get_language(\"{name}\") failed");
   |

warning: unused import: `analysis_engine::config::ServiceConfig`
 --> src/bin/load_test.rs:7:5
  |
7 | use analysis_engine::config::ServiceConfig;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `std::sync::Arc`
 --> src/bin/load_test.rs:9:5
  |
9 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `api::AppState`, `config::ServiceConfig`, and `parser::TreeSitterParser`
  --> src/bin/../../tests/load_test.rs:7:5
   |
7  |     api::AppState,
   |     ^^^^^^^^^^^^^
8  |     config::ServiceConfig,
   |     ^^^^^^^^^^^^^^^^^^^^^
9  |     models::{AnalysisRequestV2 as AnalysisRequest, AnalysisResultV2 as AnalysisResult},
10 |     parser::TreeSitterParser,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: if this is a test module, consider adding a `#[cfg(test)]` to the containing module
  --> src/bin/load_test.rs:14:1
   |
14 | mod load_test;
   | ^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src/bin/../../tests/load_test.rs:16:27
   |
16 | use tracing::{info, warn, error};
   |                           ^^^^^
   |
help: if this is a test module, consider adding a `#[cfg(test)]` to the containing module
  --> src/bin/load_test.rs:14:1
   |
14 | mod load_test;
   | ^^^^^^^^^^^^^^

warning: unused variable: `config`
   --> src/bin/../../tests/load_test.rs:131:22
    |
131 |     pub async fn new(config: LoadTestConfig) -> anyhow::Result<Self> {
    |                      ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `service`
   --> src/bin/../../tests/load_test.rs:266:9
    |
266 |         service: Arc<AnalysisService>,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_service`

warning: unused variable: `request`
   --> src/bin/../../tests/load_test.rs:267:9
    |
267 |         request: AnalysisRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: fields `name`, `expected_loc`, and `expected_files` are never read
  --> src/bin/../../tests/load_test.rs:36:9
   |
35 | pub struct TestRepository {
   |            -------------- fields in this struct
36 |     pub name: String,
   |         ^^^^
37 |     pub url: String,
38 |     pub expected_loc: usize,
   |         ^^^^^^^^^^^^
39 |     pub expected_files: usize,
   |         ^^^^^^^^^^^^^^
   |
   = note: `TestRepository` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis
   = note: `#[warn(dead_code)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/bin/../../tests/load_test.rs:187:42
    |
187 |                         let error_type = format!("{:?}", e);
    |                                          ^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
    |
187 -                         let error_type = format!("{:?}", e);
187 +                         let error_type = format!("{e:?}");
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/../../tests/load_test.rs:422:35
    |
422 |             .map(|(error, count)| format!("- {}: {}", error, count))
    |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
422 -             .map(|(error, count)| format!("- {}: {}", error, count))
422 +             .map(|(error, count)| format!("- {error}: {count}"))
    |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_tree_sitter_apis.rs:24:5
   |
24 |     println!("Testing tree-sitter-{}:", name);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
24 -     println!("Testing tree-sitter-{}:", name);
24 +     println!("Testing tree-sitter-{name}:");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_tree_sitter_apis.rs:28:13
   |
28 |             println!("  - Language name: {:?}", language);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
28 -             println!("  - Language name: {:?}", language);
28 +             println!("  - Language name: {language:?}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_parsers.rs:14:13
   |
14 |             println!("Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
14 -             println!("Failed to create parser: {}", e);
14 +             println!("Failed to create parser: {e}");
   |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:123:9
    |
123 |         print!("Testing {} parser... ", lang);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
123 -         print!("Testing {} parser... ", lang);
123 +         print!("Testing {lang} parser... ");
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:148:13
    |
148 |             println!("✗ FAILED - couldn't write temp file: {}", e);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
148 -             println!("✗ FAILED - couldn't write temp file: {}", e);
148 +             println!("✗ FAILED - couldn't write temp file: {e}");
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:162:17
    |
162 |                 println!("✗ FAILED - {}", e);
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
162 -                 println!("✗ FAILED - {}", e);
162 +                 println!("✗ FAILED - {e}");
    |

warning: variables can be used directly in the `format!` string
 --> src/bin/test_language_registry.rs:9:5
  |
9 |     println!("Languages: {:?}\n", languages);
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
  = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
  |
9 -     println!("Languages: {:?}\n", languages);
9 +     println!("Languages: {languages:?}\n");
  |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:18:17
   |
18 |                 println!("✓ {} - loaded successfully", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
18 -                 println!("✓ {} - loaded successfully", lang_name);
18 +                 println!("✓ {lang_name} - loaded successfully");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:22:17
   |
22 |                 println!("✗ {} - FAILED to load", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
22 -                 println!("✗ {} - FAILED to load", lang_name);
22 +                 println!("✗ {lang_name} - FAILED to load");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:31:9
   |
31 |         println!("Failed: {:?}", failed_languages);
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
31 -         println!("Failed: {:?}", failed_languages);
31 +         println!("Failed: {failed_languages:?}");
   |

warning: redundant pattern matching, consider using `is_some()`
  --> src/bin/test_language_registry.rs:39:16
   |
39 |         if let Some(_) = get_language(lang) {
   |         -------^^^^^^^--------------------- help: try: `if get_language(lang).is_some()`
   |
   = note: this will change drop order of the result, as well as all temporaries
   = note: add `#[allow(clippy::redundant_pattern_matching)]` if this is important
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_pattern_matching
   = note: `#[warn(clippy::redundant_pattern_matching)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:40:13
   |
40 |             println!("{}: Available ✓", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
40 -             println!("{}: Available ✓", lang);
40 +             println!("{lang}: Available ✓");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:42:13
   |
42 |             println!("{}: Not available ✗", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
42 -             println!("{}: Not available ✗", lang);
42 +             println!("{lang}: Not available ✗");
   |

warning: variables can be used directly in the `format!` string
  --> src/main.rs:48:69
   |
48 |         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {}", e)))?;
   |                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
48 -         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {}", e)))?;
48 +         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {e}")))?;
   |

warning: empty line after doc comment
 --> src/bin/test_unsafe_bindings.rs:2:1
  |
2 | / /// This tests our centralized unsafe operations in isolation
3 | |
  | |_^
4 |   use analysis_engine::parser::unsafe_bindings::{load_language_unsafe, is_language_supported, supported_languages, LanguageLoadError};
  |   - the comment documents this `use` import
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments
  = note: `#[warn(clippy::empty_line_after_doc_comments)]` on by default
  = help: if the empty line is unintentional, remove it
help: if the comment should document the crate use an inner doc comment
  |
1 ~ //! Test binary to verify unsafe_bindings module works correctly
2 ~ //! This tests our centralized unsafe operations in isolation
  |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:33:13
   |
33 |             eprintln!("   ✗ Failed to load Rust: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
33 -             eprintln!("   ✗ Failed to load Rust: {}", e);
33 +             eprintln!("   ✗ Failed to load Rust: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:45:13
   |
45 |             eprintln!("   ✗ Failed to load Python: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
45 -             eprintln!("   ✗ Failed to load Python: {}", e);
45 +             eprintln!("   ✗ Failed to load Python: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:57:13
   |
57 |             eprintln!("   ✗ Failed to load JavaScript: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
57 -             eprintln!("   ✗ Failed to load JavaScript: {}", e);
57 +             eprintln!("   ✗ Failed to load JavaScript: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:70:13
   |
70 |             println!("   ✓ Correctly rejected unsupported language: {}", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
70 -             println!("   ✓ Correctly rejected unsupported language: {}", lang);
70 +             println!("   ✓ Correctly rejected unsupported language: {lang}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:73:13
   |
73 |             eprintln!("   ✗ Unexpected error type: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
73 -             eprintln!("   ✗ Unexpected error type: {}", e);
73 +             eprintln!("   ✗ Unexpected error type: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_unsafe_bindings.rs:92:17
   |
92 |                 eprintln!("\n   Failed to load {}: {}", lang_name, e);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
92 -                 eprintln!("\n   Failed to load {}: {}", lang_name, e);
92 +                 eprintln!("\n   Failed to load {lang_name}: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:40:13
   |
40 |             eprintln!("✗ Failed to initialize AI services: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
40 -             eprintln!("✗ Failed to initialize AI services: {}", e);
40 +             eprintln!("✗ Failed to initialize AI services: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:51:13
   |
51 |             println!("✗ AI Pattern Detection test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
51 -             println!("✗ AI Pattern Detection test failed: {}", e);
51 +             println!("✗ AI Pattern Detection test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:64:13
   |
64 |             println!("✗ Code Quality Assessment test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -             println!("✗ Code Quality Assessment test failed: {}", e);
64 +             println!("✗ Code Quality Assessment test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:76:13
   |
76 |             println!("✗ Semantic Search test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
76 -             println!("✗ Semantic Search test failed: {}", e);
76 +             println!("✗ Semantic Search test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:88:13
   |
88 |             println!("✗ Repository Insights test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
88 -             println!("✗ Repository Insights test failed: {}", e);
88 +             println!("✗ Repository Insights test failed: {e}");
   |

warning: `analysis-engine` (bin "test_language_api") generated 3 warnings (run `cargo clippy --fix --bin "test_language_api"` to apply 3 suggestions)
warning: `analysis-engine` (bin "load_test") generated 10 warnings (run `cargo clippy --fix --bin "load_test"` to apply 6 suggestions)
warning: `analysis-engine` (bin "test_tree_sitter_apis") generated 2 warnings (run `cargo clippy --fix --bin "test_tree_sitter_apis"` to apply 2 suggestions)
warning: `analysis-engine` (bin "test_parsers") generated 4 warnings (run `cargo clippy --fix --bin "test_parsers"` to apply 4 suggestions)
warning: `analysis-engine` (bin "test_language_registry") generated 7 warnings (run `cargo clippy --fix --bin "test_language_registry"` to apply 6 suggestions)
warning: `analysis-engine` (bin "analysis-engine") generated 1 warning (run `cargo clippy --fix --bin "analysis-engine"` to apply 1 suggestion)
warning: `analysis-engine` (bin "test_unsafe_bindings") generated 7 warnings (run `cargo clippy --fix --bin "test_unsafe_bindings"` to apply 6 suggestions)
warning: `analysis-engine` (bin "test_ai_services") generated 5 warnings (run `cargo clippy --fix --bin "test_ai_services"` to apply 5 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.20s
