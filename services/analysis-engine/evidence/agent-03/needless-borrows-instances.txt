./src/services/embeddings.rs:                .args(&["auth", "application-default", "print-access-token"])
./src/services/code_quality_assessor.rs:                .args(&["auth", "application-default", "print-access-token"])
./src/services/analyzer/performance.rs:                .args(&["-o", "rss=", "-p", &pid.to_string()])
./src/services/embeddings_enhancement.rs:                .args(&["auth", "application-default", "print-access-token"])
./src/services/intelligent_documentation.rs:                .args(&["auth", "application-default", "print-access-token"])
./src/services/repository_insights.rs:                .args(&["auth", "application-default", "print-access-token"])
./src/services/ai_pattern_detector.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:133 |                 .args(&["-o", "rss=", "-p", &pid.to_string()])
./evidence/agent-03/clippy-warnings-detailed.txt:333 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:487 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:504 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:663 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:939 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-warnings-detailed.txt:1069 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/embeddings.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/code_quality_assessor.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/analyzer/performance.rs:                .args(&["-o", "rss=", "-p", &pid.to_string()])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/embeddings_enhancement.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/intelligent_documentation.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/repository_insights.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/needless-borrows-instances.txt:./src/services/ai_pattern_detector.rs:                .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:133 |                 .args(&["-o", "rss=", "-p", &pid.to_string()])
./evidence/agent-03/clippy-before.txt:333 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:487 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:504 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:663 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:939 |                 .args(&["auth", "application-default", "print-access-token"])
./evidence/agent-03/clippy-before.txt:1069 |                 .args(&["auth", "application-default", "print-access-token"])
