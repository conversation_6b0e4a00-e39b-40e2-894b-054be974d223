    |
    = note: clamp will panic if max < min, min.is_nan(), or max.is_nan()
    = note: clamp returns NaN if the input is NaN
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp
    = note: `#[warn(clippy::manual_clamp)]` on by default

warning: the borrowed expression implements the required traits
   --> src/services/analyzer/performance.rs:133:23
--
133 |                 .args(&["-o", "rss=", "-p", &pid.to_string()])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["-o", "rss=", "-p", &pid.to_string()]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args
    = note: `#[warn(clippy::needless_borrows_for_generic_args)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:156:17
--
    |
    = note: clamp will panic if max < min, min.is_nan(), or max.is_nan()
    = note: clamp returns NaN if the input is NaN
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp

warning: this `impl` can be derived
  --> src/services/analyzer/progress.rs:13:1
--
86 |                         e.position.map(|p| p.line as u32),
   |                                            ^^^^^^^^^^^^^ help: try: `p.line`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast
   = note: `#[warn(clippy::unnecessary_cast)]` on by default

warning: length comparison to zero
  --> src/services/analyzer/results.rs:99:12
--
   |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace with clamp: `(total_files / concurrent_file_processors).clamp(1, 50)`
   |
   = note: clamp will panic if max < min
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp

warning: you seem to use `.enumerate()` and immediately discard the index
  --> src/services/analyzer/streaming_processor.rs:71:36
--
368 |                 lines_of_code: line_count as u32,
    |                                ^^^^^^^^^^^^^^^^^ help: try: `line_count`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/services/analyzer/streaming_processor.rs:369:35
--
369 |                 total_lines: Some(line_count as u32),
    |                                   ^^^^^^^^^^^^^^^^^ help: try: `line_count`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/mod.rs:404:25
--
333 |                 .args(&["auth", "application-default", "print-access-token"])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings.rs:386:31
--
487 |                 .args(&["auth", "application-default", "print-access-token"])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: you should consider adding a `Default` implementation for `PatternDetector`
  --> src/services/pattern_detector.rs:14:5
--
504 |                 .args(&["auth", "application-default", "print-access-token"])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> src/services/code_quality_assessor.rs:412:30
--
663 |                 .args(&["auth", "application-default", "print-access-token"])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: useless use of `format!`
   --> src/services/repository_insights.rs:642:30
--
939 |                 .args(&["auth", "application-default", "print-access-token"])
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:708:26
--
1069 |                 .args(&["auth", "application-default", "print-access-token"])
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `["auth", "application-default", "print-access-token"]`
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/services/semantic_search.rs:226:37
--
    |
    = note: clamp will panic if max < min, min.is_nan(), or max.is_nan()
    = note: clamp returns NaN if the input is NaN
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp

warning: variables can be used directly in the `format!` string
   --> src/services/semantic_search.rs:535:9
--
628 |         statement.add_param("limit", &(per_page as i64));
    |                                       ^^^^^^^^^^^^^^^^^ help: try: `{ per_page }`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: casting to the same type is unnecessary (`i64` -> `i64`)
   --> src/storage/spanner.rs:629:40
--
629 |         statement.add_param("offset", &(offset as i64));
    |                                        ^^^^^^^^^^^^^^^ help: try: `{ offset }`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: variables can be used directly in the `format!` string
    --> src/storage/spanner.rs:1344:29
--
163 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/parser/adapters.rs:168:39
--
168 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
   --> src/parser/adapters.rs:179:21
--
     |
     = note: clamp will panic if max < min, min.is_nan(), or max.is_nan()
     = note: clamp returns NaN if the input is NaN
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:13:13
--
   |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace with clamp: `(self.max_size / 4).clamp(1, 4)`
   |
   = note: clamp will panic if max < min
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp

warning: manual implementation of `Option::map`
  --> src/parser/streaming/file_processor.rs:81:31
