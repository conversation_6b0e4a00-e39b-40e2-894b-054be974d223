./src/services/semantic_search.rs:        score.min(1.0).max(0.0)
./src/services/analyzer/performance.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/clippy-before.txt:274 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/clippy-before.txt:189 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./evidence/agent-03/clippy-before.txt:67 |         let initial_batch_size = (total_files / concurrent_file_processors).max(1).min(50);
./evidence/agent-03/clippy-before.txt:530 |         score.min(1.0).max(0.0)
./evidence/agent-03/clippy-before.txt:1195 |         score.max(0.0).min(100.0)
./evidence/agent-03/clippy-before.txt:84 |         let warm_up_count = (self.max_size / 4).max(1).min(4); // 25% of max size, at least 1, at most 4
./src/services/analyzer/streaming_processor.rs:        let initial_batch_size = (total_files / concurrent_file_processors).max(1).min(50);
./src/services/analyzer/file_processor.rs:        adjusted_concurrency.min(max_concurrency).max(1)
./src/services/analyzer/file_processor.rs:            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
./src/backpressure/mod.rs:        memory_factor.max(cpu_factor).max(queue_factor).min(1.0)
./src/parser/parser_pool.rs:        let warm_up_count = (self.max_size / 4).max(1).min(4); // 25% of max size, at least 1, at most 4
./src/parser/language_metrics.rs:        score.max(0.0).min(100.0)
