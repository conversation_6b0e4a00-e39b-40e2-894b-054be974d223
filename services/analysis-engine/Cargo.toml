[package]
name = "analysis-engine"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = { version = "0.8.4", features = ["ws", "macros"] }
tower = "0.5"
tower-http = { version = "0.6.6", features = ["cors", "trace", "compression-full"] }
hyper = { version = "1.0", features = ["full"] }
base64 = "0.22"

# Async runtime
tokio = { version = "1.46.1", features = ["full"] }
futures = "0.3"
async-trait = "0.1"
validator = { version = "0.20", features = ["derive"] }

# Code parsing
tree-sitter = "0.22.6"
tree-sitter-language = "0.1"
tree-sitter-rust = "0.24"
tree-sitter-javascript = "0.23"
tree-sitter-typescript = "0.23"
tree-sitter-python = "0.23"
tree-sitter-go = "0.23"
tree-sitter-java = "0.23"
tree-sitter-c = "0.24"
tree-sitter-cpp = "0.23"
# Additional languages - adding stable ones
tree-sitter-html = "0.20"
tree-sitter-css = "0.21"
tree-sitter-json = "0.21"
tree-sitter-yaml = "0.6"
tree-sitter-php = "0.23"
tree-sitter-ruby = "0.23"
tree-sitter-bash = "0.23"
tree-sitter-md = "0.3"

# Mobile development languages
tree-sitter-swift = "0.6"
tree-sitter-kotlin = "0.3"
tree-sitter-objc = "3.0"

# Data science and numerical computing
tree-sitter-r = "1.0"
tree-sitter-julia = "0.23"

circuit_breaker = "0.1.1"
bb8 = "0.8.0"
# Functional programming languages
tree-sitter-haskell = "0.15"
tree-sitter-scala = "0.23"
tree-sitter-erlang = "0.7"
tree-sitter-elixir = "0.3"

# Web and markup languages
tree-sitter-xml = "0.7"

# Systems programming languages
tree-sitter-zig = "1.1"
tree-sitter-d = "0.6"

# Other languages
tree-sitter-lua = "0.1"
tree-sitter-ocaml = "0.24"
tree-sitter-nix = "0.0.2"

# Serialization
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Error handling
anyhow = "1.0.98"
thiserror = "1.0"

# Logging
tracing = "0.1.41"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# GCP clients
google-cloud-gax = "0.19.2"
tonic = "0.12.3"
prost-types = "0.12"

# Hashing
sha2 = "0.10"

# WebSocket
tokio-tungstenite = "0.27.0"

# Parallel processing
rayon = "1.10.0"

# Concurrency and memory management
crossbeam-queue = "0.3"
num_cpus = "1.16"

# Utilities
uuid = { version = "1.17.0", features = ["serde", "v4"] }
chrono = { version = "0.4.41", features = ["serde"] }
dashmap = "6.1.0"
once_cell = "1.19"
clap = { version = "4.5", features = ["derive"] }
rand = "0.8"
scopeguard = "1.2"
google-cloud-auth = "0.16"
lazy_static = "1.5"

# Git operations
git2 = "0.19"

# Authentication
jsonwebtoken = "9.2"

# Metrics
prometheus = "0.14"

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Rate limiting
governor = "0.6"
nonzero_ext = "0.3"

# Redis for caching and rate limiting
redis = { version = "0.25", features = ["tokio-comp", "connection-manager"] }

# Hashing for cache keys
md5 = "0.7"

# Path operations
walkdir = "2.4"

# Language detection
tokei = "13.0.0-alpha.8"  # Latest alpha to avoid unmaintained dependencies

# Pattern matching
glob = "0.3"
glob-match = "0.2"
regex = "1.10"

# Alternative parsers for version-conflicted languages
sqlparser = "0.39"  # For SQL parsing without tree-sitter
quick-xml = "0.31"  # For XML parsing without tree-sitter
toml = "0.8"       # For TOML configuration files

# Configuration
config = "0.14"  # Updated to avoid yaml-rust dependency
dotenvy = "0.15"  # Maintained fork of dotenv

# Google Cloud APIs
google-cloud-googleapis = { version = "0.16.1", features = ["pubsub"] }
google-cloud-spanner = "0.33.0"
google-cloud-storage = "0.24.0"
google-cloud-pubsub = "0.30.0"
google-cloud-token = "0.1.2"

[build-dependencies]
cc = "1.0"
serde_json = "1.0"
walkdir = "2.4"

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
mockall = "0.12"
tempfile = "3.8"
wiremock = "0.6"  # Updated to avoid instant dependency
jsonschema = "0.18"
mockito = "1.0"
# Property-based testing
proptest = "1.4"
proptest-derive = "0.5"
# Mutation testing
# Test data generation
fake = { version = "2.9", features = ["derive", "chrono"] }
# Enhanced assertions
pretty_assertions = "1.4"
# Test organization and utilities
rstest = "0.21"
rstest_reuse = "0.7"
# Coverage and quality

[[bench]]
name = "analysis_bench"
harness = false

[[bench]]
name = "regex_performance"
harness = false

[[bin]]
name = "test_ai_services"
path = "src/bin/test_ai_services.rs"

[[bin]]
name = "load_test"
path = "src/bin/load_test.rs"

[[test]]
name = "ai_integration_tests"
path = "tests/ai_integration_tests.rs"

[[test]]
name = "load_test"
path = "tests/load_test.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
