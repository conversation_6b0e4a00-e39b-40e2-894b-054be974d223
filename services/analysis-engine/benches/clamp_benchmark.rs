use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn bench_manual_clamp(c: &mut Criterion) {
    c.bench_function("manual_clamp", |b| {
        b.iter(|| {
            let value = black_box(0.75);
            black_box(value.min(1.0).max(0.0))
        })
    });
}

fn bench_optimized_clamp(c: &mut Criterion) {
    c.bench_function("optimized_clamp", |b| {
        b.iter(|| {
            let value = black_box(0.75);
            black_box(value.clamp(0.0, 1.0))
        })
    });
}

criterion_group!(benches, bench_manual_clamp, bench_optimized_clamp);
criterion_main!(benches);
