//! Benchmarks for analysis engine

use analysis_engine::config::ServiceConfig;
use analysis_engine::parser::TreeSitterParser;
use analysis_engine::services::language_detector::LanguageDetector;
use analysis_engine::services::pattern_detector::PatternDetector;
use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};

use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use tempfile::TempDir;

// Sample code snippets of various sizes
const SMALL_RUST_CODE: &str = r#"
fn main() {
    println!("Hello, world!");
}

fn add(a: i32, b: i32) -> i32 {
    a + b
}

#[test]
fn test_add() {
    assert_eq!(add(2, 2), 4);
}
"#;

const MEDIUM_RUST_CODE: &str = r#"
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

pub struct Cache<K, V> {
    store: Arc<Mutex<HashMap<K, V>>>,
    max_size: usize,
}

impl<K: Clone + Eq + std::hash::Hash, V: Clone> Cache<K, V> {
    pub fn new(max_size: usize) -> Self {
        Self {
            store: Arc::new(Mutex::new(HashMap::new())),
            max_size,
        }
    }

    pub fn get(&self, key: &K) -> Option<V> {
        let store = self.store.lock().unwrap();
        store.get(key).cloned()
    }

    pub fn set(&self, key: K, value: V) {
        let mut store = self.store.lock().unwrap();
        if store.len() >= self.max_size {
            if let Some(first_key) = store.keys().next().cloned() {
                store.remove(&first_key);
            }
        }
        store.insert(key, value);
    }

    pub fn clear(&self) {
        let mut store = self.store.lock().unwrap();
        store.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_operations() {
        let cache = Cache::new(2);
        cache.set("key1", "value1");
        assert_eq!(cache.get(&"key1"), Some("value1"));
        
        cache.set("key2", "value2");
        cache.set("key3", "value3");
        
        // First key should be evicted
        assert_eq!(cache.get(&"key1"), None);
        assert_eq!(cache.get(&"key2"), Some("value2"));
        assert_eq!(cache.get(&"key3"), Some("value3"));
    }
}
"#;

fn generate_large_code(lines: usize) -> String {
    let mut code = String::new();

    for i in 0..lines / 10 {
        code.push_str(&format!(
            r#"
pub fn function_{i}(param: i32) -> Result<String, Box<dyn std::error::Error>> {{
    let result = param * 2;
    if result > 100 {{
        return Err("Value too large".into());
    }}
    let mut sum = 0;
    for j in 0..result {{
        sum += j;
    }}
    Ok(format!("Result: {{}}, Sum: {{}}", result, sum))
}}
"#,
            i = i
        ));
    }

    code
}

fn setup_test_file(content: &str) -> (TempDir, PathBuf) {
    let temp_dir = TempDir::new().unwrap();
    let file_path = temp_dir.path().join("test.rs");
    fs::write(&file_path, content).unwrap();
    (temp_dir, file_path)
}

fn bench_ast_parsing(c: &mut Criterion) {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();

    let mut group = c.benchmark_group("ast_parsing");

    // Small file benchmark
    let (_temp_dir, small_file) = setup_test_file(SMALL_RUST_CODE);
    group.bench_with_input(
        BenchmarkId::new("parse_file", "small"),
        &small_file,
        |b, file_path| {
            b.iter(|| {
                let result = tokio::runtime::Runtime::new()
                    .unwrap()
                    .block_on(parser.parse_file(file_path));
                black_box(result)
            });
        },
    );

    // Medium file benchmark
    let (_temp_dir2, medium_file) = setup_test_file(MEDIUM_RUST_CODE);
    group.bench_with_input(
        BenchmarkId::new("parse_file", "medium"),
        &medium_file,
        |b, file_path| {
            b.iter(|| {
                let result = tokio::runtime::Runtime::new()
                    .unwrap()
                    .block_on(parser.parse_file(file_path));
                black_box(result)
            });
        },
    );

    // Large file benchmark (10K lines)
    let large_code = generate_large_code(10_000);
    let (_temp_dir3, large_file) = setup_test_file(&large_code);
    group.bench_with_input(
        BenchmarkId::new("parse_file", "large_10k"),
        &large_file,
        |b, file_path| {
            b.iter(|| {
                let result = tokio::runtime::Runtime::new()
                    .unwrap()
                    .block_on(parser.parse_file(file_path));
                black_box(result)
            });
        },
    );

    group.finish();
}

fn bench_pattern_detection(c: &mut Criterion) {
    let detector = PatternDetector::new();
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();

    let mut group = c.benchmark_group("pattern_detection");

    // Parse medium file once for pattern detection
    let (_temp_dir, medium_file) = setup_test_file(MEDIUM_RUST_CODE);
    let runtime = tokio::runtime::Runtime::new().unwrap();
    let file_analysis = runtime.block_on(parser.parse_file(&medium_file)).unwrap();

    group.bench_function("detect_patterns_medium", |b| {
        b.iter(|| {
            let patterns = detector.detect_patterns(&file_analysis);
            black_box(patterns)
        });
    });

    // Large file pattern detection
    let large_code = generate_large_code(5_000);
    let (_temp_dir2, large_file) = setup_test_file(&large_code);
    let large_analysis = runtime.block_on(parser.parse_file(&large_file)).unwrap();

    group.bench_function("detect_patterns_large", |b| {
        b.iter(|| {
            let patterns = detector.detect_patterns(&large_analysis);
            black_box(patterns)
        });
    });

    group.finish();
}

fn bench_language_detection(c: &mut Criterion) {
    let detector = LanguageDetector::new();

    let mut group = c.benchmark_group("language_detection");

    // Create a directory with multiple file types
    let temp_dir = TempDir::new().unwrap();
    fs::write(temp_dir.path().join("main.rs"), SMALL_RUST_CODE).unwrap();
    fs::write(temp_dir.path().join("app.js"), "console.log('hello');").unwrap();
    fs::write(temp_dir.path().join("style.css"), "body { margin: 0; }").unwrap();
    fs::write(temp_dir.path().join("index.html"), "<html></html>").unwrap();
    fs::write(temp_dir.path().join("script.py"), "print('hello')").unwrap();

    group.bench_function("detect_languages", |b| {
        b.iter(|| {
            let result = detector.detect_languages_with_stats(temp_dir.path());
            black_box(result)
        });
    });

    group.bench_function("detect_languages_with_stats", |b| {
        b.iter(|| {
            let result = detector.detect_languages_with_stats(temp_dir.path());
            black_box(result)
        });
    });

    group.finish();
}

fn bench_concurrent_parsing(c: &mut Criterion) {
    use rayon::prelude::*;

    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();

    let mut group = c.benchmark_group("concurrent_parsing");
    group.sample_size(10); // Reduce sample size for longer benchmarks

    // Create multiple test files
    let temp_dir = TempDir::new().unwrap();
    let files: Vec<PathBuf> = (0..50)
        .map(|i| {
            let file_path = temp_dir.path().join(format!("file_{}.rs", i));
            let code = if i % 3 == 0 {
                SMALL_RUST_CODE
            } else if i % 3 == 1 {
                MEDIUM_RUST_CODE
            } else {
                &generate_large_code(1000)
            };
            fs::write(&file_path, code).unwrap();
            file_path
        })
        .collect();

    group.bench_function("parse_50_files_sequential", |b| {
        b.iter(|| {
            let runtime = tokio::runtime::Runtime::new().unwrap();
            let results: Vec<_> = files
                .iter()
                .map(|file| runtime.block_on(parser.parse_file(file)))
                .collect();
            black_box(results)
        });
    });

    group.bench_function("parse_50_files_parallel", |b| {
        b.iter(|| {
            let results: Vec<_> = files
                .par_iter()
                .map(|file| {
                    let runtime = tokio::runtime::Runtime::new().unwrap();
                    runtime.block_on(parser.parse_file(file))
                })
                .collect();
            black_box(results)
        });
    });

    group.finish();
}

fn bench_memory_usage(c: &mut Criterion) {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();

    let mut group = c.benchmark_group("memory_usage");
    group.sample_size(10);

    // Benchmark memory usage for different file sizes
    let sizes = vec![1_000, 10_000, 50_000];

    for size in sizes {
        let code = generate_large_code(size);
        let (_temp_dir, file_path) = setup_test_file(&code);

        group.bench_with_input(
            BenchmarkId::new("parse_lines", size),
            &file_path,
            |b, file_path| {
                b.iter(|| {
                    let runtime = tokio::runtime::Runtime::new().unwrap();
                    let result = runtime.block_on(parser.parse_file(file_path));
                    // Force evaluation of the result to ensure memory is allocated
                    if let Ok(analysis) = result {
                        black_box(analysis.ast.children.len());
                    }
                });
            },
        );
    }

    group.finish();
}

criterion_group!(
    benches,
    bench_ast_parsing,
    bench_pattern_detection,
    bench_language_detection,
    bench_concurrent_parsing,
    bench_memory_usage
);
criterion_main!(benches);
