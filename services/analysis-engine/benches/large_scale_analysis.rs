//! Large-scale performance benchmarks for 1M+ LOC validation
//! Agent 11B - Emergency Performance Validation

use analysis_engine::config::ServiceConfig;
use analysis_engine::services::analyzer::AnalysisService;
use analysis_engine::parser::TreeSitterParser;
use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion, Throughput};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Duration;
use tokio::runtime::Runtime;

// Test sizes from 100K to 2M LOC
const TEST_SIZES: &[usize] = &[100_000, 500_000, 1_000_000, 1_500_000, 2_000_000];

// Repository paths for different size categories
const SMALL_REPO: &str = "test-data/large-repositories/kubernetes";   // ~1-2M LOC
const MEDIUM_REPO: &str = "test-data/large-repositories/rust";        // ~2-3M LOC
const LARGE_REPO: &str = "test-data/large-repositories/tensorflow";   // ~3-5M LOC
const XLARGE_REPO: &str = "test-data/large-repositories/linux";       // ~20M+ LOC

fn get_repository_for_size(target_size: usize) -> &'static str {
    match target_size {
        100_000..=500_000 => SMALL_REPO,
        500_001..=1_000_000 => MEDIUM_REPO,
        1_000_001..=1_500_000 => LARGE_REPO,
        _ => XLARGE_REPO,
    }
}

fn check_repository_exists(path: &str) -> bool {
    Path::new(path).exists()
}

fn create_analyzer() -> AnalysisService {
    let rt = Runtime::new().unwrap();
    rt.block_on(async {
        AnalysisService::new().await.expect("Failed to create analyzer")
    })
}

fn bench_large_scale_analysis(c: &mut Criterion) {
    // Skip if repositories haven't been cloned yet
    if !check_repository_exists(SMALL_REPO) {
        eprintln!("WARNING: Test repositories not found. Run collect-test-repositories.sh first.");
        eprintln!("Skipping large scale benchmarks.");
        return;
    }

    let rt = Arc::new(Runtime::new().unwrap());
    let analyzer = rt.block_on(async { AnalysisService::new().await.unwrap() });
    
    let mut group = c.benchmark_group("large_scale_analysis");
    group.sample_size(10); // Reduce sample size for long-running benchmarks
    group.measurement_time(Duration::from_secs(600)); // 10 minutes max per benchmark
    
    // Test actual repositories
    let test_repos = vec![
        ("kubernetes", SMALL_REPO),
        ("rust", MEDIUM_REPO),
        ("tensorflow", LARGE_REPO),
    ];
    
    for (name, repo_path) in test_repos {
        if !check_repository_exists(repo_path) {
            eprintln!("Skipping {} - repository not found", name);
            continue;
        }
        
        // Set throughput based on expected repository size
        let expected_loc = match name {
            "kubernetes" => 1_500_000,
            "rust" => 2_500_000,
            "tensorflow" => 3_500_000,
            _ => 1_000_000,
        };
        
        group.throughput(Throughput::Elements(expected_loc as u64));
        
        group.bench_with_input(
            BenchmarkId::new("real_repository", name),
            &repo_path,
            |b, &repo_path| {
                let rt = rt.clone();
                let analyzer = &analyzer;
                b.to_async(&*rt).iter(|| async {
                    let result = analyzer.analyze_repository(black_box(repo_path)).await;
                    match result {
                        Ok(analysis) => black_box(analysis),
                        Err(e) => panic!("Analysis failed: {}", e),
                    }
                });
            },
        );
    }
    
    group.finish();
}

fn bench_memory_usage_scaling(c: &mut Criterion) {
    if !check_repository_exists(SMALL_REPO) {
        eprintln!("WARNING: Test repositories not found. Skipping memory benchmarks.");
        return;
    }

    let rt = Arc::new(Runtime::new().unwrap());
    let analyzer = rt.block_on(async { AnalysisService::new().await.unwrap() });
    
    let mut group = c.benchmark_group("memory_usage_scaling");
    group.sample_size(5); // Even smaller sample size for memory-intensive benchmarks
    
    // Test memory usage with different repository sizes
    let test_repos = vec![
        ("small_1m", SMALL_REPO),
        ("medium_2m", MEDIUM_REPO),
    ];
    
    for (name, repo_path) in test_repos {
        if !check_repository_exists(repo_path) {
            continue;
        }
        
        group.bench_with_input(
            BenchmarkId::new("memory_peak", name),
            &repo_path,
            |b, &repo_path| {
                let rt = rt.clone();
                let analyzer = &analyzer;
                b.iter_custom(|iters| {
                    let rt = rt.clone();
                    rt.block_on(async move {
                        let mut total_duration = Duration::from_secs(0);
                        
                        for _ in 0..iters {
                            let start = std::time::Instant::now();
                            
                            // Analyze repository
                            let _result = analyzer.analyze_repository(black_box(repo_path)).await;
                            
                            total_duration += start.elapsed();
                            
                            // Note: In real implementation, we would track memory usage
                            // using the MemoryTracker from the PRP
                        }
                        
                        total_duration
                    })
                });
            },
        );
    }
    
    group.finish();
}

fn bench_concurrent_analysis(c: &mut Criterion) {
    if !check_repository_exists(SMALL_REPO) {
        eprintln!("WARNING: Test repositories not found. Skipping concurrent benchmarks.");
        return;
    }

    let rt = Arc::new(Runtime::new().unwrap());
    let analyzer = Arc::new(rt.block_on(async { AnalysisService::new().await.unwrap() }));
    
    let mut group = c.benchmark_group("concurrent_analysis");
    group.sample_size(3); // Very small sample size for concurrent tests
    
    let concurrency_levels = [1, 3, 5];
    
    for &concurrency in &concurrency_levels {
        group.bench_with_input(
            BenchmarkId::new("concurrent_1m_loc", concurrency),
            &concurrency,
            |b, &concurrency| {
                let rt = rt.clone();
                let analyzer = analyzer.clone();
                
                b.to_async(&*rt).iter(|| async {
                    let repo_path = SMALL_REPO; // Use smallest repo for concurrent tests
                    
                    let tasks: Vec<_> = (0..concurrency)
                        .map(|_| {
                            let analyzer = analyzer.clone();
                            let path = repo_path.to_string();
                            tokio::spawn(async move {
                                analyzer.analyze_repository(black_box(&path)).await
                            })
                        })
                        .collect();
                    
                    // Wait for all analyses to complete
                    for task in tasks {
                        match task.await {
                            Ok(Ok(analysis)) => black_box(analysis),
                            Ok(Err(e)) => panic!("Analysis failed: {}", e),
                            Err(e) => panic!("Task failed: {}", e),
                        };
                    }
                });
            },
        );
    }
    
    group.finish();
}

fn bench_streaming_analysis(c: &mut Criterion) {
    if !check_repository_exists(SMALL_REPO) {
        eprintln!("WARNING: Test repositories not found. Skipping streaming benchmarks.");
        return;
    }

    let rt = Arc::new(Runtime::new().unwrap());
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = TreeSitterParser::new(config).unwrap();
    
    let mut group = c.benchmark_group("streaming_analysis");
    group.sample_size(5);
    
    // Test streaming analysis of large files vs batch processing
    group.bench_function("streaming_1m_loc", |b| {
        let rt = rt.clone();
        let parser = &parser;
        
        b.to_async(&*rt).iter(|| async {
            let repo_path = Path::new(SMALL_REPO);
            
            // Simulate streaming analysis by processing files one at a time
            // with memory cleanup between batches
            let mut file_count = 0;
            let mut total_loc = 0;
            
            for entry in walkdir::WalkDir::new(repo_path)
                .into_iter()
                .filter_map(|e| e.ok())
                .filter(|e| e.file_type().is_file())
                .filter(|e| is_source_file(e.path()))
            {
                let result = parser.parse_file(entry.path()).await;
                if let Ok(analysis) = result {
                    total_loc += analysis.metrics.lines_of_code;
                    file_count += 1;
                    
                    // Simulate memory cleanup every 100 files
                    if file_count % 100 == 0 {
                        // In real implementation, we would free AST memory here
                        tokio::task::yield_now().await;
                    }
                }
            }
            
            black_box((file_count, total_loc))
        });
    });
    
    group.finish();
}

fn is_source_file(path: &Path) -> bool {
    let extensions = vec![
        "rs", "py", "go", "js", "ts", "tsx", "jsx", "java", "cpp", "c", "cc", 
        "cxx", "h", "hpp", "rb", "php", "cs", "swift", "kt", "m", "mm", "scala",
    ];
    
    if let Some(ext) = path.extension() {
        extensions.contains(&ext.to_string_lossy().to_lowercase().as_str())
    } else {
        false
    }
}

fn bench_incremental_analysis(c: &mut Criterion) {
    if !check_repository_exists(SMALL_REPO) {
        eprintln!("WARNING: Test repositories not found. Skipping incremental benchmarks.");
        return;
    }

    let rt = Arc::new(Runtime::new().unwrap());
    let analyzer = rt.block_on(async { AnalysisService::new().await.unwrap() });
    
    let mut group = c.benchmark_group("incremental_analysis");
    group.sample_size(5);
    
    // First, perform initial analysis
    let initial_result = rt.block_on(async {
        analyzer.analyze_repository(SMALL_REPO).await.unwrap()
    });
    
    group.bench_function("incremental_update_single_file", |b| {
        let rt = rt.clone();
        let analyzer = &analyzer;
        let initial = &initial_result;
        
        b.to_async(&*rt).iter(|| async {
            // Simulate incremental update of a single file
            // In real implementation, this would only re-analyze changed files
            let changed_file = Path::new(SMALL_REPO).join("README.md");
            
            // For now, just parse the single file
            let config = Arc::new(ServiceConfig::from_env().unwrap());
            let parser = TreeSitterParser::new(config).unwrap();
            let result = parser.parse_file(&changed_file).await;
            
            black_box(result)
        });
    });
    
    group.finish();
}

// Helper function to create criterion config with extended timeout
fn create_criterion() -> Criterion {
    Criterion::default()
        .measurement_time(Duration::from_secs(60))
        .warm_up_time(Duration::from_secs(3))
        .sample_size(10)
        .noise_threshold(0.05)
}

criterion_group! {
    name = large_scale_benches;
    config = create_criterion();
    targets = 
        bench_large_scale_analysis,
        bench_memory_usage_scaling,
        bench_concurrent_analysis,
        bench_streaming_analysis,
        bench_incremental_analysis
}

criterion_main!(large_scale_benches);