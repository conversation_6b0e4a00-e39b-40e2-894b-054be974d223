# Agent 07B - Independent PRP Alignment Verification INITIAL.md

## CRITICAL CONTEXT
You are an independent verification agent with NO access to <PERSON><PERSON>lau<PERSON> or any previous agent's work. You must perform a fresh, unbiased PRP alignment assessment for the analysis-engine service. Another agent (07) has already completed this task, but due to the critical importance of accurate assessment before production deployment, we need an independent second opinion.

## FEATURE:
Conduct an independent, comprehensive Product Requirements Prompt (PRP) alignment assessment for the analysis-engine service. Verify implementation against ALL documented requirements, identify gaps, analyze architectural decisions, and provide a prioritized roadmap. This is a VERIFICATION mission - assume nothing, trust nothing, verify everything.

**Your Unique Requirements:**
1. **Fresh Perspective**: Do NOT read Agent 07's work until AFTER you complete your assessment
2. **Evidence-First**: Every finding must have code proof (file path + line numbers)
3. **Skeptical Approach**: Question all claims, especially "97% complete" and "3% remaining"
4. **Deep Verification**: Actually run commands to verify claims (don't trust documentation)
5. **Independent Validation**: Create your own test scripts to verify functionality

## EXAMPLES:
**Critical Files to Analyze (verify these exist first):**
- `PRPs/services/analysis-engine.md` - Primary service requirements (CHECK EVERY CLAIM)
- `PRPs/architecture-patterns.md` - Architecture requirements
- `services/analysis-engine/src/main.rs` - Check JWT middleware status (line ~52)
- `services/analysis-engine/src/api/handlers/analysis.rs` - Language endpoint implementation
- `services/analysis-engine/src/parser/unsafe_bindings.rs` - Count actual languages
- `services/analysis-engine/Dockerfile` - Production deployment config
- `services/analysis-engine/cloudbuild.yaml` - Cloud Run deployment
- `.claudedocs/orchestration/analysis-engine-prod-tracker.md` - Current status claims

**Verification Commands to Run:**
```bash
# Count actual language support
grep -c "tree_sitter_" services/analysis-engine/src/parser/unsafe_bindings.rs

# Check JWT implementation
grep -n "jwt\|JWT" services/analysis-engine/src/main.rs
grep -rn "JwtMiddleware" services/analysis-engine/src/

# Verify WebSocket implementation
grep -rn "WebSocket\|websocket" services/analysis-engine/src/

# Check test coverage
cd services/analysis-engine && cargo test --no-run 2>&1 | grep -c "test result"

# Verify performance benchmarks exist
find services/analysis-engine -name "*bench*" -o -name "*perf*" | grep -E "\.(rs|toml)$"
```

## DOCUMENTATION:

**Research Requirements (Read these critically):**
```yaml
primary_sources:
  - services/analysis-engine/README.md: Check if claims match implementation
  - docs/analysis-engine/: Production documentation (if exists)
  - ai-agent-prompts/phase4-features/01-repository-analysis-api.md: Phase 4 requirements
  - validation-results/: Any existing validation evidence
  
verification_focus:
  - "97% complete" claim: Calculate actual completion independently
  - "3% remaining" tasks: Verify if JWT, Cloud Run, languages are really missing
  - Performance claims: Look for evidence of "1M LOC in <5 minutes" testing
  - Language support: Count actual vs documented languages
  - Integration readiness: Verify Pattern Mining integration points
```

**Critical Questions to Answer:**
1. Is the JWT middleware really commented out or is it active?
2. How many languages are ACTUALLY supported vs documented?
3. Has anyone actually tested the 1M LOC performance claim?
4. Is WebSocket streaming implemented or not?
5. What's the REAL completion percentage based on requirements?

## OTHER CONSIDERATIONS:

**Independent Verification Methodology:**
```yaml
Step 1 - Baseline Truth:
  - Run verification commands first
  - Count actual implementations
  - Create your own requirement list from PRPs
  - Don't trust any existing summaries

Step 2 - Deep Code Analysis:
  - Read actual implementation files
  - Trace through the codebase
  - Look for hidden features not in docs
  - Check for commented-out code

Step 3 - Test Evidence:
  - Look for actual test files
  - Check if performance tests exist
  - Verify integration tests
  - Run tests if possible

Step 4 - Documentation Audit:
  - Compare all docs against code
  - Look for outdated information
  - Check commit history for changes
  - Identify documentation drift

Step 5 - Independent Calculation:
  - Create your own scoring matrix
  - Weight features by importance
  - Calculate actual completion %
  - Document your methodology
```

**Red Flags to Watch For:**
- Claims without evidence
- Percentages without calculation methodology  
- "Complete" features that are commented out
- Missing test files for claimed features
- Documentation that contradicts code
- Performance claims without benchmarks

**Your Deliverables (Independent Versions):**
1. **Independent Compliance Matrix** - Your own requirement mapping
2. **Verification Report** - What's real vs claimed
3. **Actual Completion Analysis** - Your calculation with methodology
4. **Documentation Accuracy Report** - All discrepancies found
5. **Risk Assessment** - Production readiness from fresh eyes

**Evidence Structure You Must Create:**
```
validation-results/
└── phase2-assessment/
    └── agent-07b-independent-verification/
        ├── verification-report.md         # Your main findings
        ├── actual-completion-analysis.md  # Your calculations
        ├── documentation-audit.md         # All discrepancies
        ├── risk-assessment.md            # Production risks
        └── evidence/
            ├── verification-scripts/     # Scripts you create
            ├── command-outputs/          # Results of verification
            └── code-analysis/            # Your code findings
```

## CRITICAL INSTRUCTIONS:

1. **DO NOT** read Agent 07's assessment until you complete yours
2. **DO NOT** trust any claims without verification
3. **DO NOT** accept documentation at face value
4. **CREATE** your own test scripts and verification methods
5. **DOCUMENT** every command you run and its output
6. **QUESTION** everything, especially nice round numbers like "97%"

## Success Criteria for Agent 07B:
- [ ] Independent requirement extraction completed
- [ ] All verification commands run and documented
- [ ] Actual code analyzed (not just grepped)
- [ ] Independent completion percentage calculated
- [ ] All documentation discrepancies identified
- [ ] Production risks assessed with evidence
- [ ] Zero trust in existing claims - everything verified

## Final Note:
After you complete your independent assessment, THEN you may read Agent 07's work at `validation-results/phase2-assessment/agent-07-prp-alignment/` to compare findings. Document where you agree, disagree, or found different issues.

Your independent verification is CRITICAL for production deployment confidence. Be thorough, be skeptical, be evidence-based.

---

## How This INITIAL.md Will Be Used

This will be provided to an AI agent that does NOT have SuperClaude access. They will:
1. Perform completely independent analysis
2. Create their own evidence and calculations  
3. Verify all claims with actual commands
4. Provide unbiased second opinion
5. Compare with Agent 07 only AFTER completion

This independent verification ensures we have high confidence in the actual state of the analysis-engine before production deployment.